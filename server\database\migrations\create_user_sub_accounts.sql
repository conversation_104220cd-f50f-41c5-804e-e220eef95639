-- AI分拣系统子账号表结构
-- 创建时间: 2025-01-31
-- 说明: 基于现有fs_user表创建子账号系统

-- 1. 为fs_user表添加AI分拣相关字段（如果还没有添加）
ALTER TABLE `fs_user` 
ADD COLUMN IF NOT EXISTS `parent_id` int(11) DEFAULT 0 COMMENT '上级会员ID' AFTER `status`,
ADD COLUMN IF NOT EXISTS `level` int(11) DEFAULT 1 COMMENT '会员等级' AFTER `parent_id`,
ADD COLUMN IF NOT EXISTS `max_sub_accounts` int(11) DEFAULT 0 COMMENT '最大子账号数量' AFTER `level`,
ADD COLUMN IF NOT EXISTS `current_sub_accounts` int(11) DEFAULT 0 COMMENT '当前子账号数量' AFTER `max_sub_accounts`,
ADD COLUMN IF NOT EXISTS `expire_time` datetime DEFAULT NULL COMMENT '到期时间' AFTER `current_sub_accounts`,
ADD COLUMN IF NOT EXISTS `is_ai_member` tinyint(1) DEFAULT 0 COMMENT '是否AI分拣会员:1是,0否' AFTER `expire_time`;

-- 2. 创建子账号表 (fs_user_sub_accounts)
CREATE TABLE IF NOT EXISTS `fs_user_sub_accounts` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '子账号ID',
  `parent_user_id` int(11) NOT NULL COMMENT '主会员ID，关联fs_user.id',
  `username` varchar(50) NOT NULL COMMENT '子账号用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `nickname` varchar(100) DEFAULT '' COMMENT '昵称',
  `phone` varchar(20) DEFAULT '' COMMENT '手机号',
  `email` varchar(100) DEFAULT '' COMMENT '邮箱',
  `avatar` varchar(255) DEFAULT '' COMMENT '头像',
  `account_type` varchar(20) NOT NULL COMMENT '账号类型：storage=入库员，sorting=分拣员，order_entry=录单员，finance=财务员',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0=禁用，1=启用',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT '' COMMENT '最后登录IP',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  KEY `parent_user_id` (`parent_user_id`),
  KEY `account_type` (`account_type`),
  KEY `status` (`status`),
  CONSTRAINT `fk_sub_accounts_user` FOREIGN KEY (`parent_user_id`) REFERENCES `fs_user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户子账号表';

-- 3. 创建用户模块权限表（适用于主会员和子账号）
CREATE TABLE IF NOT EXISTS `fs_user_modules` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(11) DEFAULT NULL COMMENT '主会员ID，关联fs_user.id',
  `sub_account_id` int(11) DEFAULT NULL COMMENT '子账号ID，关联fs_user_sub_accounts.id',
  `module_name` varchar(50) NOT NULL COMMENT '模块名称：storage=入库，sorting=分拣，order_entry=录单，finance=财务',
  `module_title` varchar(100) NOT NULL COMMENT '模块标题',
  `is_enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用：0=禁用，1=启用',
  `expire_time` datetime DEFAULT NULL COMMENT '模块到期时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `sub_account_id` (`sub_account_id`),
  KEY `module_name` (`module_name`),
  KEY `expire_time` (`expire_time`),
  CONSTRAINT `fk_user_modules_user` FOREIGN KEY (`user_id`) REFERENCES `fs_user` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_user_modules_sub_account` FOREIGN KEY (`sub_account_id`) REFERENCES `fs_user_sub_accounts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `chk_user_or_sub_account` CHECK (
    (`user_id` IS NOT NULL AND `sub_account_id` IS NULL) OR 
    (`user_id` IS NULL AND `sub_account_id` IS NOT NULL)
  )
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户模块权限表';

-- 4. 创建索引优化查询性能
CREATE INDEX `idx_user_module` ON `fs_user_modules` (`user_id`, `module_name`);
CREATE INDEX `idx_sub_account_module` ON `fs_user_modules` (`sub_account_id`, `module_name`);

-- 5. 插入测试数据（可选）
-- 创建一个AI分拣测试主会员
INSERT INTO `fs_user` (`username`, `password`, `nickname`, `phone`, `email`, `status`, `is_ai_member`, `max_sub_accounts`, `created_at`) VALUES
('ai_member_test', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'AI分拣测试会员', '***********', '<EMAIL>', 1, 1, 10, NOW())
ON DUPLICATE KEY UPDATE username = username;

-- 为测试主会员添加所有模块权限
SET @test_user_id = (SELECT id FROM fs_user WHERE username = 'ai_member_test');
INSERT INTO `fs_user_modules` (`user_id`, `module_name`, `module_title`, `is_enabled`) VALUES
(@test_user_id, 'storage', '入库管理', 1),
(@test_user_id, 'sorting', '分拣管理', 1),
(@test_user_id, 'order_entry', '录单管理', 1),
(@test_user_id, 'finance', '财务管理', 1)
ON DUPLICATE KEY UPDATE module_name = module_name;

-- 创建测试子账号
INSERT INTO `fs_user_sub_accounts` (`parent_user_id`, `username`, `password`, `nickname`, `account_type`, `status`, `created_at`) VALUES
(@test_user_id, 'storage_worker_01', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '入库员01', 'storage', 1, NOW()),
(@test_user_id, 'sorting_worker_01', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '分拣员01', 'sorting', 1, NOW()),
(@test_user_id, 'order_entry_01', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '录单员01', 'order_entry', 1, NOW()),
(@test_user_id, 'finance_worker_01', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '财务员01', 'finance', 1, NOW())
ON DUPLICATE KEY UPDATE username = username;

-- 为子账号添加对应的模块权限
SET @storage_sub_id = (SELECT id FROM fs_user_sub_accounts WHERE username = 'storage_worker_01');
SET @sorting_sub_id = (SELECT id FROM fs_user_sub_accounts WHERE username = 'sorting_worker_01');
SET @order_entry_sub_id = (SELECT id FROM fs_user_sub_accounts WHERE username = 'order_entry_01');
SET @finance_sub_id = (SELECT id FROM fs_user_sub_accounts WHERE username = 'finance_worker_01');

INSERT INTO `fs_user_modules` (`sub_account_id`, `module_name`, `module_title`, `is_enabled`) VALUES
(@storage_sub_id, 'storage', '入库管理', 1),
(@sorting_sub_id, 'sorting', '分拣管理', 1),
(@order_entry_sub_id, 'order_entry', '录单管理', 1),
(@finance_sub_id, 'finance', '财务管理', 1)
ON DUPLICATE KEY UPDATE module_name = module_name;
