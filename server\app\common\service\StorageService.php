<?php
declare(strict_types=1);

namespace app\common\service;

use app\common\model\PurchaseOrder;
use app\common\model\PurchaseOrderItem;
use app\common\model\Inventory;
use app\common\model\User;
use think\facade\Db;

class StorageService
{
    /**
     * 获取进货单列表
     */
    public static function getPurchaseOrderList(array $params = []): array
    {
        $page = $params['current'] ?? 1;
        $limit = $params['size'] ?? 20;

        $query = PurchaseOrder::with(['user', 'creator', 'items'])
            ->order('id desc');

        // 搜索条件
        if (!empty($params['order_no'])) {
            $query->orderNo($params['order_no']);
        }

        if (!empty($params['supplier_name'])) {
            $query->supplier($params['supplier_name']);
        }

        if (isset($params['status']) && $params['status'] !== '') {
            $query->status($params['status']);
        }

        if (!empty($params['user_id'])) {
            $query->userId($params['user_id']);
        }

        if (!empty($params['start_date']) || !empty($params['end_date'])) {
            $query->dateRange($params['start_date'] ?? '', $params['end_date'] ?? '');
        }

        $result = $query->paginate([
            'list_rows' => $limit,
            'page' => $page,
        ]);

        return [
            'list' => $result->items(),
            'total' => $result->total(),
            'current' => $page,
            'size' => $limit,
        ];
    }

    /**
     * 创建进货单
     */
    public static function createPurchaseOrder(array $data): PurchaseOrder
    {
        Db::startTrans();
        try {
            // 生成订单号
            $data['order_no'] = PurchaseOrder::generateOrderNo();
            
            // 创建进货单
            $purchaseOrder = PurchaseOrder::create([
                'order_no' => $data['order_no'],
                'user_id' => $data['user_id'],
                'supplier_name' => $data['supplier_name'],
                'supplier_contact' => $data['supplier_contact'] ?? '',
                'supplier_phone' => $data['supplier_phone'] ?? '',
                'status' => PurchaseOrder::STATUS_PENDING,
                'remark' => $data['remark'] ?? '',
                'created_by' => $data['created_by'],
            ]);

            // 创建商品明细
            $totalAmount = 0;
            if (!empty($data['items'])) {
                foreach ($data['items'] as $item) {
                    $amount = $item['quantity'] * $item['unit_price'];
                    $totalAmount += $amount;

                    PurchaseOrderItem::create([
                        'purchase_order_id' => $purchaseOrder->id,
                        'product_name' => $item['product_name'],
                        'product_sku' => $item['product_sku'] ?? '',
                        'product_spec' => $item['product_spec'] ?? '',
                        'quantity' => $item['quantity'],
                        'unit_price' => $item['unit_price'],
                        'amount' => $amount,
                    ]);
                }
            }

            // 更新总金额
            $purchaseOrder->save(['total_amount' => $totalAmount]);

            Db::commit();
            return $purchaseOrder;
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 更新进货单
     */
    public static function updatePurchaseOrder(int $id, array $data): bool
    {
        $purchaseOrder = PurchaseOrder::find($id);
        if (!$purchaseOrder) {
            throw new \Exception('进货单不存在');
        }

        if (!$purchaseOrder->canCancel()) {
            throw new \Exception('当前状态不允许修改');
        }

        Db::startTrans();
        try {
            // 更新进货单基本信息
            $purchaseOrder->save([
                'supplier_name' => $data['supplier_name'],
                'supplier_contact' => $data['supplier_contact'] ?? '',
                'supplier_phone' => $data['supplier_phone'] ?? '',
                'remark' => $data['remark'] ?? '',
            ]);

            // 更新商品明细
            if (!empty($data['items'])) {
                // 删除原有明细
                PurchaseOrderItem::where('purchase_order_id', $id)->delete();

                // 创建新明细
                $totalAmount = 0;
                foreach ($data['items'] as $item) {
                    $amount = $item['quantity'] * $item['unit_price'];
                    $totalAmount += $amount;

                    PurchaseOrderItem::create([
                        'purchase_order_id' => $id,
                        'product_name' => $item['product_name'],
                        'product_sku' => $item['product_sku'] ?? '',
                        'product_spec' => $item['product_spec'] ?? '',
                        'quantity' => $item['quantity'],
                        'unit_price' => $item['unit_price'],
                        'amount' => $amount,
                    ]);
                }

                // 更新总金额
                $purchaseOrder->save(['total_amount' => $totalAmount]);
            }

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 删除进货单
     */
    public static function deletePurchaseOrder(int $id): bool
    {
        $purchaseOrder = PurchaseOrder::find($id);
        if (!$purchaseOrder) {
            throw new \Exception('进货单不存在');
        }

        if (!$purchaseOrder->canCancel()) {
            throw new \Exception('当前状态不允许删除');
        }

        Db::startTrans();
        try {
            // 删除明细
            PurchaseOrderItem::where('purchase_order_id', $id)->delete();
            
            // 删除进货单
            $purchaseOrder->delete();

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 入库操作
     */
    public static function storageOperation(int $id, int $operatorId): bool
    {
        $purchaseOrder = PurchaseOrder::with('items')->find($id);
        if (!$purchaseOrder) {
            throw new \Exception('进货单不存在');
        }

        if (!$purchaseOrder->canStorage()) {
            throw new \Exception('当前状态不允许入库');
        }

        Db::startTrans();
        try {
            // 执行入库操作
            $result = $purchaseOrder->doStorage($operatorId);

            Db::commit();
            return $result;
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }

    /**
     * 获取进货单详情
     */
    public static function getPurchaseOrderDetail(int $id): ?PurchaseOrder
    {
        return PurchaseOrder::with(['user', 'creator', 'items'])->find($id);
    }

    /**
     * 获取用户列表（用于选择）
     */
    public static function getUserList(): array
    {
        return User::where('is_ai_member', 1)
            ->where('status', 1)
            ->field('id,username,nickname,phone')
            ->select()
            ->toArray();
    }

    /**
     * 获取库存列表
     */
    public static function getInventoryList(array $params = []): array
    {
        $page = $params['current'] ?? 1;
        $limit = $params['size'] ?? 20;

        $query = Inventory::with('user')->order('id desc');

        // 搜索条件
        if (!empty($params['user_id'])) {
            $query->where('user_id', $params['user_id']);
        }

        if (!empty($params['product_name'])) {
            $query->whereLike('product_name', '%' . $params['product_name'] . '%');
        }

        if (!empty($params['product_sku'])) {
            $query->whereLike('product_sku', '%' . $params['product_sku'] . '%');
        }

        $result = $query->paginate([
            'list_rows' => $limit,
            'page' => $page,
        ]);

        return [
            'list' => $result->items(),
            'total' => $result->total(),
            'current' => $page,
            'size' => $limit,
        ];
    }

    /**
     * 获取统计数据
     */
    public static function getStatistics(int $userId = null): array
    {
        $query = PurchaseOrder::query();
        
        if ($userId) {
            $query->where('user_id', $userId);
        }

        return [
            'total_orders' => $query->count(),
            'pending_orders' => $query->where('status', PurchaseOrder::STATUS_PENDING)->count(),
            'stored_orders' => $query->where('status', PurchaseOrder::STATUS_STORED)->count(),
            'total_amount' => $query->sum('total_amount'),
            'pending_amount' => $query->where('status', PurchaseOrder::STATUS_PENDING)->sum('total_amount'),
        ];
    }
}
