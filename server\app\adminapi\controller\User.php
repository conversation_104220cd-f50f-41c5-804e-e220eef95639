<?php
declare(strict_types=1);

namespace app\adminapi\controller;

use app\common\controller\BaseController;
use app\common\model\User as UserModel;
use app\adminapi\validate\UserValidate;
use think\facade\Db;

/**
 * 用户管理控制器
 */
class User extends BaseController
{
    /**
     * 获取用户列表（包含AI分拣会员）
     */
    public function list()
    {
        $params = $this->request->get();
        $current = (int)($params['current'] ?? 1);
        $size = (int)($params['size'] ?? 20);
        $name = $params['name'] ?? '';
        $phone = $params['phone'] ?? '';
        $where = [];
        if ($name) {
            $where[] = ['username|nickname', 'like', "%{$name}%"];
        }
        if ($phone) {
            $where[] = ['phone', 'like', "%{$phone}%"];
        }

        $list = UserModel::where($where)
            ->with(['referrer'])
            ->page($current, $size)
            ->order('created_at desc')
            ->select();

        $total = UserModel::where($where)->count();

        // 数据转换
        $records = $list->map(function($item) {
            return [
                'id' => $item->id,
                'userName' => $item->username,
                'nickName' => $item->nickname,
                'userPhone' => $item->phone,
                'userEmail' => $item->email,
                'avatar' => $item->avatar,
                'referrer' => $item->referrer_name,
                'userStatus' => $item->status,
                'userStatusText' => $item->status_text,
                'maxSubAccounts' => $item->max_sub_accounts ?? 0,
                'currentSubAccounts' => $item->current_sub_accounts ?? 0,
                'expireTime' => $item->expire_time,
                'expireStatus' => $this->getExpireStatus($item->expire_time),
                'createTime' => $item->created_at,
                'updateTime' => $item->updated_at
            ];
        })->toArray();

        return $this->success([
            'records' => $records,
            'total' => $total,
            'current' => $current,
            'size' => $size
        ]);
    }

    /**
     * 获取到期状态
     */
    private function getExpireStatus($expireTime)
    {
        if (empty($expireTime)) {
            return '永久';
        }

        $expireTimestamp = strtotime($expireTime);
        $now = time();

        if ($expireTimestamp > $now) {
            $days = ceil(($expireTimestamp - $now) / 86400);
            return $days > 0 ? "剩余{$days}天" : '今日到期';
        } else {
            return '已到期';
        }
    }

    /**
     * 创建用户
     */
    public function create()
    {
        $data = $this->request->post();

        if (empty($data)) {
            return $this->error('请提供有效的数据');
        }

        // 创建时必填字段验证
        if (empty($data['nickName'])) {
            return $this->error('昵称不能为空');
        }
        if (empty($data['password'])) {
            return $this->error('密码不能为空');
        }
        if (empty($data['userPhone'])) {
            return $this->error('手机号不能为空');
        }

        // 数据验证
        $validate = new UserValidate();
        if (!$validate->scene('create')->check($data)) {
            return $this->error($validate->getError());
        }

        try {
            Db::startTrans();

            // 生成用户名（如果没有提供）
            $username = $data['userName'] ?? 'user_' . time() . rand(1000, 9999);

            // 转换字段名
            $userData = [
                'username' => $username,
                'nickname' => $data['nickName'],
                'password' => password_hash($data['password'], PASSWORD_DEFAULT),
                'phone' => $data['userPhone'],
                'avatar' => $data['avatar'] ?? '',
                'email' => $data['userEmail'] ?? '',
                'status' => $data['userStatus'] ?? 1,
                // 用户到期时间和子账号相关字段
                'expire_time' => $data['expireTime'] ?? null,
                'max_sub_accounts' => $data['maxSubAccounts'] ?? 0,
                'current_sub_accounts' => 0
            ];

            // 如果有支付密码，加密存储
            if (!empty($data['payPassword'])) {
                $userData['pay_password'] = password_hash($data['payPassword'], PASSWORD_DEFAULT);
            }

            // 如果有推荐人，查找推荐人ID
            if (!empty($data['referrer'])) {
                $referrerUser = UserModel::where('phone', $data['referrer'])
                    ->whereOr('username', $data['referrer'])
                    ->find();
                if ($referrerUser) {
                    $userData['referrer_id'] = $referrerUser->id;
                }
            }

            // 创建用户 - 使用与Admin控制器相同的方式
            $user = new UserModel();
            $user->save($userData);

            Db::commit();
            return $this->success($user->toArray(), '创建成功');
        } catch (\Exception $e) {
            Db::rollback();
            return $this->error('创建失败：' . $e->getMessage());
        }
    }

    /**
     * 更新用户
     */
    public function update()
    {
        try {
            $id = (int)$this->request->param('id');
            $data = $this->request->put();

            if ($id <= 0) {
                return $this->error('无效的用户ID');
            }

            if (empty($data)) {
                return $this->error('请提供有效的更新数据');
            }

            // 数据验证
            $validate = new UserValidate();
            if (!$validate->scene('update')->check($data)) {
                return $this->error($validate->getError());
            }

            $user = UserModel::find($id);
            if (!$user) {
                return $this->error('用户不存在');
            }

            // 转换字段名
            $userData = [];
            if (isset($data['userName'])) $userData['username'] = $data['userName'];
            if (isset($data['nickName'])) $userData['nickname'] = $data['nickName'];
            if (isset($data['userPhone'])) $userData['phone'] = $data['userPhone'];
            if (isset($data['userEmail'])) $userData['email'] = $data['userEmail'];
            if (isset($data['avatar'])) $userData['avatar'] = $data['avatar'];

            // 状态字段：如果前端传了值就更新
            if (isset($data['userStatus'])) $userData['status'] = $data['userStatus'];

            // 如果传了密码，则加密后更新
            if (!empty($data['password'])) {
                $userData['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
            }

            // 如果传了支付密码，则加密后更新
            if (!empty($data['payPassword'])) {
                $userData['pay_password'] = password_hash($data['payPassword'], PASSWORD_DEFAULT);
            }

            $user->save($userData);
            return $this->success($user->toArray(), '更新成功');
        } catch (\Exception $e) {
            return $this->error('更新失败：' . $e->getMessage());
        }
    }

    /**
     * 删除用户
     */
    public function delete()
    {
        try {
            $id = (int)$this->request->param('id');

            if ($id <= 0) {
                return $this->error('无效的用户ID');
            }

            $user = UserModel::find($id);
            if (!$user) {
                return $this->error('用户不存在');
            }

            $user->delete();
            return $this->success([], '删除成功');
        } catch (\Exception $e) {
            return $this->error('删除失败：' . $e->getMessage());
        }
    }

    /**
     * 设置用户模块权限
     */
    public function setModules()
    {
        try {
            $data = $this->request->post();
            $userId = $data['userId'] ?? 0;
            $modules = $data['modules'] ?? [];

            if ($userId <= 0) {
                return $this->error('无效的用户ID');
            }

            $user = UserModel::find($userId);
            if (!$user) {
                return $this->error('用户不存在');
            }

            Db::startTrans();

            // 删除用户现有的模块权限
            Db::table('fs_user_modules')->where('user_id', $userId)->delete();

            // 添加新的模块权限
            if (!empty($modules)) {
                $moduleData = [];
                foreach ($modules as $module) {
                    // 模块到期时间逻辑：
                    // 1. 如果模块设置了独立到期时间，使用模块的时间
                    // 2. 如果模块没有设置时间（null），则跟随用户到期时间
                    $moduleExpireTime = null;
                    if (isset($module['useUserExpireTime']) && $module['useUserExpireTime']) {
                        // 跟随用户到期时间，设置为null，在验证时动态获取用户到期时间
                        $moduleExpireTime = null;
                    } elseif (!empty($module['expireTime'])) {
                        // 使用模块独立的到期时间
                        $moduleExpireTime = $module['expireTime'];
                    }

                    $moduleData[] = [
                        'user_id' => $userId,
                        'module_name' => $module['name'],
                        'module_title' => $module['title'],
                        'is_enabled' => $module['enabled'] ?? 1,
                        'expire_time' => $moduleExpireTime,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ];
                }
                Db::table('fs_user_modules')->insertAll($moduleData);
            }

            // 更新用户的子账号限制
            if (isset($data['maxSubAccounts'])) {
                $user->max_sub_accounts = $data['maxSubAccounts'];
                $user->save();
            }

            Db::commit();
            return $this->success([], '模块权限设置成功');
        } catch (\Exception $e) {
            Db::rollback();
            return $this->error('设置失败：' . $e->getMessage());
        }
    }

    /**
     * 获取用户模块权限
     */
    public function getModules()
    {
        try {
            $id = (int)$this->request->param('id');

            if ($id <= 0) {
                return $this->error('无效的用户ID');
            }

            $modules = Db::table('fs_user_modules')
                ->where('user_id', $id)
                ->order('created_at desc')
                ->select();

            return $this->success($modules);
        } catch (\Exception $e) {
            return $this->error('获取失败：' . $e->getMessage());
        }
    }

    /**
     * 创建子账号
     */
    public function createSubAccount()
    {
        try {
            $data = $this->request->post();
            $parentUserId = $data['parentUserId'] ?? 0;

            if ($parentUserId <= 0) {
                return $this->error('请指定主会员ID');
            }

            // 检查主会员
            $parentUser = UserModel::find($parentUserId);
            if (!$parentUser || !$parentUser->is_ai_member) {
                return $this->error('主会员不存在或不是AI分拣会员');
            }

            // 检查子账号数量限制
            if ($parentUser->current_sub_accounts >= $parentUser->max_sub_accounts) {
                return $this->error('子账号数量已达上限');
            }

            Db::startTrans();

            // 创建子账号
            $subAccountData = [
                'parent_user_id' => $parentUserId,
                'username' => $data['username'],
                'password' => password_hash($data['password'], PASSWORD_DEFAULT),
                'nickname' => $data['nickname'] ?? '',
                'phone' => $data['phone'] ?? '',
                'email' => $data['email'] ?? '',
                'account_type' => $data['accountType'], // storage, sorting, order_entry, finance
                'status' => $data['status'] ?? 1
            ];

            Db::table('fs_user_sub_accounts')->insert($subAccountData);

            // 更新主会员的子账号数量
            $parentUser->current_sub_accounts += 1;
            $parentUser->save();

            Db::commit();
            return $this->success([], '子账号创建成功');
        } catch (\Exception $e) {
            Db::rollback();
            return $this->error('创建失败：' . $e->getMessage());
        }
    }

    /**
     * 获取用户的子账号列表
     */
    public function getSubAccounts()
    {
        try {
            $id = (int)$this->request->param('id');

            if ($id <= 0) {
                return $this->error('无效的用户ID');
            }

            $subAccounts = Db::table('fs_user_sub_accounts')
                ->where('parent_user_id', $id)
                ->order('created_at desc')
                ->select();

            return $this->success($subAccounts);
        } catch (\Exception $e) {
            return $this->error('获取失败：' . $e->getMessage());
        }
    }

    /**
     * 删除子账号
     */
    public function deleteSubAccount()
    {
        try {
            $id = (int)$this->request->param('id');

            if ($id <= 0) {
                return $this->error('无效的子账号ID');
            }

            Db::startTrans();

            // 获取子账号信息
            $subAccount = Db::table('fs_user_sub_accounts')->where('id', $id)->find();
            if (!$subAccount) {
                return $this->error('子账号不存在');
            }

            // 删除子账号
            Db::table('fs_user_sub_accounts')->where('id', $id)->delete();

            // 更新主会员的子账号数量
            $parentUser = UserModel::find($subAccount['parent_user_id']);
            if ($parentUser && $parentUser->current_sub_accounts > 0) {
                $parentUser->current_sub_accounts -= 1;
                $parentUser->save();
            }

            Db::commit();
            return $this->success([], '子账号删除成功');
        } catch (\Exception $e) {
            Db::rollback();
            return $this->error('删除失败：' . $e->getMessage());
        }
    }

    /**
     * 检查用户模块权限（考虑双重时间控制）
     */
    public function checkModulePermission()
    {
        try {
            $userId = $this->request->param('userId');
            $moduleName = $this->request->param('moduleName');

            if (!$userId || !$moduleName) {
                return $this->error('参数不完整');
            }

            // 获取用户信息
            $user = UserModel::find($userId);
            if (!$user) {
                return $this->error('用户不存在');
            }

            // 检查用户账号是否到期
            if ($user->expire_time && strtotime($user->expire_time) <= time()) {
                return $this->success(['hasPermission' => false, 'reason' => '用户账号已到期']);
            }

            // 获取模块权限
            $module = Db::table('fs_user_modules')
                ->where('user_id', $userId)
                ->where('module_name', $moduleName)
                ->where('is_enabled', 1)
                ->find();

            if (!$module) {
                return $this->success(['hasPermission' => false, 'reason' => '未开通此模块']);
            }

            // 检查模块是否到期
            if ($module['expire_time']) {
                // 模块有独立到期时间
                if (strtotime($module['expire_time']) <= time()) {
                    return $this->success(['hasPermission' => false, 'reason' => '模块已到期']);
                }
            } else {
                // 模块跟随用户到期时间，已在上面检查过用户到期时间
            }

            return $this->success(['hasPermission' => true, 'reason' => '有权限访问']);
        } catch (\Exception $e) {
            return $this->error('检查失败：' . $e->getMessage());
        }
    }

    /**
     * 获取用户可用模块列表（考虑双重时间控制）
     */
    public function getAvailableModules()
    {
        try {
            $userId = $this->request->param('userId');

            if (!$userId) {
                return $this->error('用户ID不能为空');
            }

            // 获取用户信息
            $user = UserModel::find($userId);
            if (!$user) {
                return $this->error('用户不存在');
            }

            $currentTime = time();
            $userExpired = $user->expire_time && strtotime($user->expire_time) <= $currentTime;

            // 获取用户的所有模块
            $modules = Db::table('fs_user_modules')
                ->where('user_id', $userId)
                ->where('is_enabled', 1)
                ->select();

            $availableModules = [];
            foreach ($modules as $module) {
                $moduleStatus = [
                    'name' => $module['module_name'],
                    'title' => $module['module_title'],
                    'available' => true,
                    'reason' => ''
                ];

                // 检查用户账号是否到期
                if ($userExpired) {
                    $moduleStatus['available'] = false;
                    $moduleStatus['reason'] = '用户账号已到期';
                } elseif ($module['expire_time']) {
                    // 模块有独立到期时间
                    if (strtotime($module['expire_time']) <= $currentTime) {
                        $moduleStatus['available'] = false;
                        $moduleStatus['reason'] = '模块已到期';
                    }
                }

                $availableModules[] = $moduleStatus;
            }

            return $this->success($availableModules);
        } catch (\Exception $e) {
            return $this->error('获取失败：' . $e->getMessage());
        }
    }
}
