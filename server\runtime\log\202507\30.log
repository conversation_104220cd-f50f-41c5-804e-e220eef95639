[2025-07-30T09:47:28+08:00][sql] CONNECT:[ UseTime:0.002336s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-30T09:47:28+08:00][sql] SHOW FULL COLUMNS FROM `fs_categories` [ RunTime:0.001114s ]
[2025-07-30T09:47:28+08:00][sql] SELECT * FROM `fs_categories` [ RunTime:0.000501s ]
[2025-07-30T09:47:28+08:00][sql] SHOW FULL COLUMNS FROM `fs_articles` [ RunTime:0.001193s ]
[2025-07-30T09:47:28+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_articles` WHERE  `category_id` = 1  AND `status` = 1 [ RunTime:0.000598s ]
[2025-07-30T09:47:28+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_articles` WHERE  `category_id` = 2  AND `status` = 1 [ RunTime:0.000496s ]
[2025-07-30T09:47:28+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_articles` WHERE  `category_id` = 3  AND `status` = 1 [ RunTime:0.000619s ]
[2025-07-30T09:47:28+08:00][sql] INSERT INTO `fs_categories` SET `name` = '测试分类_094728' , `slug` = '' , `description` = '' , `parent_id` = 0 , `status` = 1 , `sort` = 0 [ RunTime:0.004629s ]
[2025-07-30T09:47:28+08:00][sql] SELECT `id`,`title`,`category_id`,`status` FROM `fs_articles` [ RunTime:0.000392s ]
[2025-07-30T09:47:28+08:00][sql] SELECT * FROM `fs_categories` [ RunTime:0.000458s ]
[2025-07-30T09:47:28+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_articles` WHERE  `category_id` = 1  AND `status` = 1 [ RunTime:0.000611s ]
[2025-07-30T09:47:28+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_articles` WHERE  `category_id` = 2  AND `status` = 1 [ RunTime:0.000486s ]
[2025-07-30T09:47:28+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_articles` WHERE  `category_id` = 3  AND `status` = 1 [ RunTime:0.000482s ]
[2025-07-30T09:47:28+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_articles` WHERE  `category_id` = 5  AND `status` = 1 [ RunTime:0.000452s ]
[2025-07-30T09:48:26+08:00][sql] CONNECT:[ UseTime:0.002016s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-30T09:48:26+08:00][sql] DESCRIBE fs_categories [ RunTime:0.000955s ]
[2025-07-30T09:48:26+08:00][sql] SHOW FULL COLUMNS FROM `fs_categories` [ RunTime:0.000889s ]
[2025-07-30T09:48:26+08:00][sql] SELECT * FROM `fs_categories` ORDER BY `id` DESC LIMIT 3 [ RunTime:0.000525s ]
[2025-07-30T09:49:10+08:00][sql] CONNECT:[ UseTime:0.002822s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-30T09:49:10+08:00][sql] SHOW FULL COLUMNS FROM `fs_categories` [ RunTime:0.001530s ]
[2025-07-30T09:49:10+08:00][sql] INSERT INTO `fs_categories` SET `name` = '测试分类_094910' , `slug` = '' , `description` = '' , `parent_id` = 0 , `status` = 1 , `sort` = 0 , `created_at` = '2025-07-30 09:49:10.560672' , `updated_at` = '2025-07-30 09:49:10.560739' [ RunTime:0.001707s ]
[2025-07-30T09:49:57+08:00][sql] CONNECT:[ UseTime:0.002156s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-30T09:49:57+08:00][sql] SHOW FULL COLUMNS FROM `fs_categories` [ RunTime:0.002247s ]
[2025-07-30T09:49:57+08:00][sql] DELETE FROM `fs_categories` WHERE  `name` LIKE '测试分类_%' [ RunTime:0.001360s ]
[2025-07-30T10:44:33+08:00][sql] CONNECT:[ UseTime:0.002073s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-30T10:44:33+08:00][sql] SHOW FULL COLUMNS FROM `fs_notices` [ RunTime:0.000962s ]
[2025-07-30T10:44:33+08:00][sql] SELECT * FROM `fs_notices` LIMIT 3 [ RunTime:0.000490s ]
[2025-07-30T10:45:08+08:00][sql] CONNECT:[ UseTime:0.002393s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-30T10:45:08+08:00][sql] DESCRIBE fs_notices [ RunTime:0.001548s ]
[2025-07-30T10:45:35+08:00][sql] CONNECT:[ UseTime:0.002415s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-30T10:45:36+08:00][sql] ALTER TABLE fs_notices ADD COLUMN cover_image VARCHAR(255) DEFAULT '' COMMENT '封面图片' AFTER content [ RunTime:0.084231s ]
[2025-07-30T10:45:36+08:00][sql] DESCRIBE fs_notices [ RunTime:0.000996s ]
[2025-07-30T10:54:47+08:00][sql] CONNECT:[ UseTime:0.002146s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-30T10:54:47+08:00][sql] SHOW FULL COLUMNS FROM `fs_notices` [ RunTime:0.002216s ]
[2025-07-30T10:54:47+08:00][sql] SELECT * FROM `fs_notices` WHERE  `id` = 3 LIMIT 1 [ RunTime:0.000493s ]
[2025-07-30T11:01:24+08:00][sql] CONNECT:[ UseTime:0.002257s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-30T11:01:24+08:00][sql] SHOW FULL COLUMNS FROM `fs_notices` [ RunTime:0.002183s ]
[2025-07-30T11:01:24+08:00][sql] SELECT * FROM `fs_notices` WHERE  `id` = 3 LIMIT 1 [ RunTime:0.000462s ]
[2025-07-30T11:11:25+08:00][sql] CONNECT:[ UseTime:0.016998s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-30T11:11:25+08:00][sql] SHOW FULL COLUMNS FROM `fs_notices` [ RunTime:0.002214s ]
[2025-07-30T11:11:25+08:00][sql] SELECT * FROM `fs_notices` WHERE  `id` = 1 LIMIT 1 [ RunTime:0.000443s ]
[2025-07-30T11:20:30+08:00][error] [0]Call to undefined method think\Request::create()[D:\fanshop\test_notice_api.php:13]
[2025-07-30T11:21:05+08:00][sql] CONNECT:[ UseTime:0.002359s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-30T11:21:05+08:00][sql] SHOW FULL COLUMNS FROM `fs_notices` [ RunTime:0.001181s ]
[2025-07-30T11:21:05+08:00][sql] SELECT * FROM `fs_notices` WHERE  `id` = 1 LIMIT 1 [ RunTime:0.000628s ]
[2025-07-30T16:24:55+08:00][sql] CONNECT:[ UseTime:0.002796s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-30T16:24:55+08:00][sql] SHOW FULL COLUMNS FROM `fs_notices` [ RunTime:0.001885s ]
[2025-07-30T16:24:55+08:00][sql] INSERT INTO `fs_notices` SET `title` = '测试公告标题' , `content` = '这是一个测试公告的内容' , `cover_image` = '' , `is_top` = 0 , `status` = 1 , `sort` = 0 , `type` = 1 , `is_published` = 1 , `published_at` = '2025-07-30 16:24:55' , `created_at` = '2025-07-30 16:24:55.941691' , `updated_at` = '2025-07-30 16:24:55.941716' [ RunTime:0.001891s ]
[2025-07-30T16:24:55+08:00][sql] SELECT * FROM `fs_notices` WHERE  `id` = 6 LIMIT 1 [ RunTime:0.000778s ]
[2025-07-30T16:24:55+08:00][sql] UPDATE `fs_notices`  SET `title` = '更新后的公告标题' , `content` = '更新后的公告内容' , `is_top` = 1 , `sort` = 10 , `updated_at` = '2025-07-30 16:24:55.946989'  WHERE  `id` = 6 [ RunTime:0.001760s ]
[2025-07-30T16:24:55+08:00][sql] DELETE FROM `fs_notices` WHERE  `id` = 6 [ RunTime:0.001621s ]
[2025-07-30T17:14:40+08:00][sql] CONNECT:[ UseTime:0.002409s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-30T17:14:40+08:00][sql] SHOW FULL COLUMNS FROM `fs_admin` [ RunTime:0.001025s ]
[2025-07-30T17:14:40+08:00][sql] SELECT * FROM `fs_admin` WHERE  `username` = 'admin' LIMIT 1 [ RunTime:0.000734s ]
[2025-07-30T17:15:18+08:00][sql] CONNECT:[ UseTime:0.002287s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-30T17:15:18+08:00][sql] SHOW FULL COLUMNS FROM `fs_admin` [ RunTime:0.001544s ]
[2025-07-30T17:15:18+08:00][sql] SELECT * FROM `fs_admin` LIMIT 3 [ RunTime:0.000564s ]
[2025-07-30T17:15:18+08:00][sql] SELECT * FROM `fs_admin` WHERE  `username` = 'admin' LIMIT 1 [ RunTime:0.000617s ]
[2025-07-30T17:15:46+08:00][sql] CONNECT:[ UseTime:0.002439s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-30T17:15:46+08:00][sql] SHOW FULL COLUMNS FROM `fs_admin` [ RunTime:0.000895s ]
[2025-07-30T17:15:46+08:00][sql] SELECT * FROM `fs_admin` LIMIT 3 [ RunTime:0.000591s ]
[2025-07-30T17:15:47+08:00][sql] SELECT * FROM `fs_admin` WHERE  `username` = 'admin' LIMIT 1 [ RunTime:0.000780s ]
[2025-07-30T17:16:13+08:00][sql] CONNECT:[ UseTime:0.002440s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-30T17:16:13+08:00][sql] SHOW FULL COLUMNS FROM `fs_admin` [ RunTime:0.001638s ]
[2025-07-30T17:16:13+08:00][sql] SELECT * FROM `fs_admin` LIMIT 3 [ RunTime:0.000622s ]
[2025-07-30T17:16:13+08:00][sql] SELECT * FROM `fs_admin` WHERE  `username` = 'admin' LIMIT 1 [ RunTime:0.000833s ]
[2025-07-30T17:16:14+08:00][sql] UPDATE `fs_admin`  SET `last_login_time` = '2025-07-30 17:16:13' , `last_login_ip` = '0.0.0.0'  WHERE  `id` = 1 [ RunTime:0.001973s ]
[2025-07-30T17:22:19+08:00][sql] CONNECT:[ UseTime:0.002232s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-30T17:22:19+08:00][sql] SHOW FULL COLUMNS FROM `fs_admin` [ RunTime:0.000951s ]
[2025-07-30T17:22:19+08:00][sql] SELECT * FROM `fs_admin` WHERE  `username` = 'admin' LIMIT 1 [ RunTime:0.000499s ]
[2025-07-30T17:22:19+08:00][sql] UPDATE `fs_admin`  SET `last_login_time` = '2025-07-30 17:22:19'  WHERE  `id` = 1 [ RunTime:0.002164s ]
[2025-07-30T17:22:19+08:00][sql] SHOW FULL COLUMNS FROM `fs_notices` [ RunTime:0.001421s ]
[2025-07-30T17:22:19+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_notices` [ RunTime:0.000395s ]
[2025-07-30T17:22:19+08:00][sql] SELECT * FROM `fs_notices` ORDER BY `is_top` DESC,`sort` DESC,`id` DESC LIMIT 0,5 [ RunTime:0.000464s ]
[2025-07-30T17:22:19+08:00][sql] SHOW FULL COLUMNS FROM `fs_articles` [ RunTime:0.000931s ]
[2025-07-30T17:22:19+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_articles` [ RunTime:0.000536s ]
[2025-07-30T17:22:19+08:00][sql] SELECT * FROM `fs_articles` ORDER BY `sort` DESC,`id` DESC LIMIT 0,5 [ RunTime:0.000465s ]
[2025-07-30T17:35:49+08:00][sql] CONNECT:[ UseTime:0.002551s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-30T17:35:49+08:00][sql] SHOW FULL COLUMNS FROM `fs_user` [ RunTime:0.003964s ]
[2025-07-30T17:35:49+08:00][sql] SELECT * FROM `fs_user` LIMIT 5 [ RunTime:0.000564s ]
[2025-07-30T17:36:28+08:00][sql] CONNECT:[ UseTime:0.002641s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-30T17:36:28+08:00][sql] DESCRIBE fs_user [ RunTime:0.003719s ]
[2025-07-30T17:36:28+08:00][sql] SHOW FULL COLUMNS FROM `fs_user` [ RunTime:0.000863s ]
[2025-07-30T17:36:28+08:00][sql] SELECT * FROM `fs_user` LIMIT 5 [ RunTime:0.000395s ]
[2025-07-30T17:36:28+08:00][sql] INSERT INTO `fs_user` SET `username` = 'testuser' , `nickname` = '测试用户' , `password` = '$2y$10$RVKujNzscwDbsBafXmq.UOdWsMYY5gLQtPa.IgZMLSb7ahmSjSSUK' , `gender` = 1 , `phone` = '13800138000' , `email` = '<EMAIL>' , `status` = 1 [ RunTime:0.002287s ]
[2025-07-30T17:36:28+08:00][sql] SELECT * FROM `fs_user` LIMIT 5 [ RunTime:0.000654s ]
[2025-07-30T17:47:50+08:00][sql] CONNECT:[ UseTime:0.002597s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-30T17:47:50+08:00][sql] DESCRIBE fs_user [ RunTime:0.001313s ]
[2025-07-30T17:47:50+08:00][sql] ALTER TABLE fs_user ADD COLUMN pay_password VARCHAR(255) DEFAULT '' COMMENT '支付密码' AFTER password [ RunTime:0.075653s ]
[2025-07-30T17:47:50+08:00][sql] ALTER TABLE fs_user ADD COLUMN referrer_id INT(11) DEFAULT 0 COMMENT '推荐人ID' AFTER phone [ RunTime:0.075236s ]
[2025-07-30T17:51:32+08:00][sql] CONNECT:[ UseTime:0.019247s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-30T17:51:32+08:00][sql] SHOW FULL COLUMNS FROM `fs_user` [ RunTime:0.004327s ]
[2025-07-30T17:52:22+08:00][sql] CONNECT:[ UseTime:0.002574s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-30T17:52:22+08:00][sql] SHOW FULL COLUMNS FROM `fs_user` [ RunTime:0.003942s ]
[2025-07-30T17:52:22+08:00][sql] INSERT INTO `fs_user` SET `username` = 'user_17538691424006' , `nickname` = '新用户测试' , `password` = '$2y$10$Plz43VOgqFmauqEQu8UkNeubOohvCRsjxFzIUtPGzcywkjyCP4KK2' , `phone` = '13900139001' , `pay_password` = '$2y$10$DmD5p73sgt4/fzvdNTG6beV2ekZdG50c3QLxdYY6ymxYqPgw.HTYe' , `avatar` = '' , `gender` = 1 , `email` = '<EMAIL>' , `status` = 1 , `referrer_id` = 0 [ RunTime:0.002078s ]
[2025-07-30T17:52:22+08:00][sql] INSERT INTO `fs_user` SET `username` = 'user_17538691424026' , `nickname` = '被推荐用户' , `password` = '$2y$10$qchfvl56Umi4.zuNO4RIE.vn9S.6dXDst3UD2y6abHKgu8Ae0LyRe' , `phone` = '13900139002' , `avatar` = '' , `gender` = 2 , `status` = 1 , `referrer_id` = 3 [ RunTime:0.001552s ]
[2025-07-30T17:52:23+08:00][sql] SELECT * FROM `fs_user` WHERE  `id` = 3 LIMIT 1 [ RunTime:0.000665s ]
[2025-07-30T17:52:23+08:00][sql] DELETE FROM `fs_user` WHERE  `id` = 3 [ RunTime:0.001688s ]
[2025-07-30T17:52:23+08:00][sql] DELETE FROM `fs_user` WHERE  `id` = 4 [ RunTime:0.001648s ]
[2025-07-30T17:53:09+08:00][error] [0]Call to undefined method think\Request::create()[D:\fanshop\test_user_api.php:12]
[2025-07-30T17:53:33+08:00][error] [0]Too few arguments to function app\common\controller\BaseController::__construct(), 0 passed in D:\fanshop\test_user_api.php on line 17 and exactly 1 expected[D:\fanshop\server\app\common\controller\BaseController.php:30]
[2025-07-30T18:31:07+08:00][sql] CONNECT:[ UseTime:0.016121s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-30T18:31:07+08:00][sql] SHOW TABLES [ RunTime:0.000858s ]
[2025-07-30T18:32:03+08:00][sql] CONNECT:[ UseTime:0.002754s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-30T18:32:03+08:00][sql] SHOW TABLES [ RunTime:0.001046s ]
[2025-07-30T18:32:03+08:00][sql] SHOW FULL COLUMNS FROM `fs_user` [ RunTime:0.001566s ]
[2025-07-30T18:32:03+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_user` [ RunTime:0.000652s ]
[2025-07-30T18:32:03+08:00][sql] INSERT INTO `fs_user` SET `username` = 'test_1753871523' , `nickname` = '测试用户' , `password` = '$2y$10$cCRRi1X3KtHBqXtzDu.xAu9C7D3rinzd9BYGVvbZ/QhEU/TxdnpOS' , `phone` = '13900139114' , `status` = 1 [ RunTime:0.001705s ]
[2025-07-30T18:32:04+08:00][sql] DELETE FROM `fs_user` WHERE  `id` = 5 [ RunTime:0.001776s ]
