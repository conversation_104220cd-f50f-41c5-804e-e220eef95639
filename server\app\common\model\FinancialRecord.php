<?php
declare(strict_types=1);

namespace app\common\model;

use think\Model;

/**
 * 财务记录模型
 */
class FinancialRecord extends Model
{
    // 表名
    protected $table = 'fs_financial_records';

    // 主键
    protected $pk = 'id';

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // 字段类型转换
    protected $type = [
        'member_id' => 'integer',
        'amount' => 'float',
        'source_id' => 'integer',
        'record_date' => 'date',
    ];

    // 获取器 - 类型文本
    public function getTypeTextAttr($value, $data)
    {
        $types = [
            'income' => '收入',
            'cost' => '成本',
            'profit' => '利润'
        ];
        return $types[$data['type']] ?? '未知';
    }

    // 获取器 - 来源类型文本
    public function getSourceTypeTextAttr($value, $data)
    {
        $types = [
            'order' => '订单',
            'purchase' => '进货'
        ];
        return $types[$data['source_type']] ?? '未知';
    }

    // 关联会员
    public function member()
    {
        return $this->belongsTo(Member::class, 'member_id', 'id');
    }
}
