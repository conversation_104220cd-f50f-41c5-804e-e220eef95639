<?php
declare(strict_types=1);

namespace app\common\model;

use think\Model;
use think\model\relation\BelongsTo;

/**
 * 进货单明细模型
 */
class PurchaseOrderItem extends Model
{
    // 表名
    protected $table = 'fs_purchase_order_items';

    // 主键
    protected $pk = 'id';

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // 字段类型转换
    protected $type = [
        'purchase_order_id' => 'integer',
        'quantity' => 'integer',
        'unit_price' => 'float',
        'amount' => 'float',
    ];

    /**
     * 关联进货单
     */
    public function purchaseOrder(): BelongsTo
    {
        return $this->belongsTo(PurchaseOrder::class, 'purchase_order_id');
    }

    /**
     * 修改器 - 自动计算小计
     */
    public function setQuantityAttr($value, $data)
    {
        // 当数量或单价变化时，自动计算小计
        if (isset($data['unit_price'])) {
            $this->amount = $value * $data['unit_price'];
        }
        return $value;
    }

    /**
     * 修改器 - 自动计算小计
     */
    public function setUnitPriceAttr($value, $data)
    {
        // 当数量或单价变化时，自动计算小计
        if (isset($data['quantity'])) {
            $this->amount = $data['quantity'] * $value;
        }
        return $value;
    }

    /**
     * 获取器 - 格式化金额
     */
    public function getAmountTextAttr($value, $data): string
    {
        return '¥' . number_format($data['amount'], 2);
    }

    /**
     * 获取器 - 格式化单价
     */
    public function getUnitPriceTextAttr($value, $data): string
    {
        return '¥' . number_format($data['unit_price'], 2);
    }

    /**
     * 计算小计
     */
    public function calculateAmount(): float
    {
        $amount = $this->quantity * $this->unit_price;
        $this->amount = $amount;
        return $amount;
    }

    /**
     * 搜索范围 - 按商品名称
     */
    public function scopeProductName($query, $productName)
    {
        if (!empty($productName)) {
            $query->whereLike('product_name', '%' . $productName . '%');
        }
    }

    /**
     * 搜索范围 - 按商品SKU
     */
    public function scopeProductSku($query, $productSku)
    {
        if (!empty($productSku)) {
            $query->whereLike('product_sku', '%' . $productSku . '%');
        }
    }
}
