<?php
declare(strict_types=1);

namespace app\common\model;

use think\Model;
use think\model\relation\HasMany;
use think\model\relation\BelongsTo;

/**
 * 进货单模型
 */
class PurchaseOrder extends Model
{
    // 表名
    protected $table = 'fs_purchase_orders';

    // 主键
    protected $pk = 'id';

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // 字段类型转换
    protected $type = [
        'total_amount' => 'float',
        'status' => 'integer',
        'user_id' => 'integer',
        'created_by' => 'integer',
    ];

    // 状态常量
    const STATUS_PENDING = 1;    // 待入库
    const STATUS_STORED = 2;     // 已入库
    const STATUS_CANCELLED = 3;  // 已取消

    /**
     * 状态文本映射
     */
    public static function getStatusText(int $status): string
    {
        $statusMap = [
            self::STATUS_PENDING => '待入库',
            self::STATUS_STORED => '已入库',
            self::STATUS_CANCELLED => '已取消',
        ];

        return $statusMap[$status] ?? '未知';
    }

    /**
     * 获取器 - 状态文本
     */
    public function getStatusTextAttr($value, $data): string
    {
        return self::getStatusText($data['status']);
    }

    /**
     * 获取器 - 状态标签类型
     */
    public function getStatusTypeAttr($value, $data): string
    {
        $typeMap = [
            self::STATUS_PENDING => 'warning',
            self::STATUS_STORED => 'success',
            self::STATUS_CANCELLED => 'danger',
        ];

        return $typeMap[$data['status']] ?? 'info';
    }

    /**
     * 关联进货单明细
     */
    public function items(): HasMany
    {
        return $this->hasMany(PurchaseOrderItem::class, 'purchase_order_id');
    }

    /**
     * 关联用户（会员）
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联创建人（管理员）
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(Admin::class, 'created_by');
    }

    /**
     * 生成进货单号
     */
    public static function generateOrderNo(): string
    {
        $prefix = 'PO';
        $date = date('Ymd');
        $random = str_pad((string)mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);

        return $prefix . $date . $random;
    }

    /**
     * 计算进货单总金额
     */
    public function calculateTotalAmount(): float
    {
        $total = $this->items()->sum('amount');

        // 更新总金额
        $this->save(['total_amount' => $total]);

        return (float)$total;
    }

    /**
     * 检查是否可以入库
     */
    public function canStorage(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * 检查是否可以取消
     */
    public function canCancel(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * 入库操作
     */
    public function doStorage(int $operatorId): bool
    {
        if (!$this->canStorage()) {
            throw new \Exception('当前状态不允许入库');
        }

        // 更新状态
        $result = $this->save([
            'status' => self::STATUS_STORED,
            'storage_by' => $operatorId,
            'storage_time' => date('Y-m-d H:i:s'),
        ]);

        if ($result) {
            // 更新库存
            $this->updateInventory();
        }

        return $result;
    }

    /**
     * 更新库存
     */
    protected function updateInventory(): void
    {
        $items = $this->items;

        foreach ($items as $item) {
            // 查找或创建库存记录
            $inventory = Inventory::where([
                'user_id' => $this->user_id,
                'product_sku' => $item->product_sku ?: $item->product_name,
            ])->find();

            if ($inventory) {
                // 更新现有库存
                $inventory->quantity += $item->quantity;
                $inventory->total_value = $inventory->quantity * $inventory->unit_price;
                $inventory->save();
            } else {
                // 创建新库存记录
                Inventory::create([
                    'user_id' => $this->user_id,
                    'product_name' => $item->product_name,
                    'product_sku' => $item->product_sku ?: $item->product_name,
                    'product_spec' => $item->product_spec,
                    'quantity' => $item->quantity,
                    'unit_price' => $item->unit_price,
                    'total_value' => $item->amount,
                ]);
            }
        }
    }

    /**
     * 取消进货单
     */
    public function doCancel(string $reason = ''): bool
    {
        if (!$this->canCancel()) {
            throw new \Exception('当前状态不允许取消');
        }

        return $this->save([
            'status' => self::STATUS_CANCELLED,
            'remark' => $this->remark . "\n取消原因：" . $reason,
        ]);
    }

    /**
     * 搜索范围 - 按订单号
     */
    public function scopeOrderNo($query, $orderNo)
    {
        if (!empty($orderNo)) {
            $query->whereLike('order_no', '%' . $orderNo . '%');
        }
    }

    /**
     * 搜索范围 - 按供应商
     */
    public function scopeSupplier($query, $supplier)
    {
        if (!empty($supplier)) {
            $query->whereLike('supplier_name', '%' . $supplier . '%');
        }
    }

    /**
     * 搜索范围 - 按状态
     */
    public function scopeStatus($query, $status)
    {
        if ($status !== '' && $status !== null) {
            $query->where('status', $status);
        }
    }

    /**
     * 搜索范围 - 按用户
     */
    public function scopeUserId($query, $userId)
    {
        if (!empty($userId)) {
            $query->where('user_id', $userId);
        }
    }

    /**
     * 搜索范围 - 按日期范围
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        if (!empty($startDate)) {
            $query->whereTime('created_at', '>=', $startDate);
        }
        if (!empty($endDate)) {
            $query->whereTime('created_at', '<=', $endDate . ' 23:59:59');
        }
    }
}
