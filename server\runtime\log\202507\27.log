[2025-07-27T00:34:25+08:00][sql] CONNECT:[ UseTime:0.002449s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-27T00:34:25+08:00][sql] SHOW FULL COLUMNS FROM `fs_admin` [ RunTime:0.001608s ]
[2025-07-27T00:34:25+08:00][sql] UPDATE `fs_admin`  SET `avatar` = 'admin_avatar.jpg' , `updated_at` = '2025-07-27 00:34:25'  WHERE  `id` = 1 [ RunTime:0.002146s ]
[2025-07-27T00:34:25+08:00][sql] SELECT * FROM `fs_admin` WHERE  `id` = 1 LIMIT 1 [ RunTime:0.000676s ]
[2025-07-27T00:35:01+08:00][sql] CONNECT:[ UseTime:0.002417s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-27T00:35:01+08:00][sql] SHOW FULL COLUMNS FROM `fs_user` [ RunTime:0.000972s ]
[2025-07-27T00:35:01+08:00][sql] SELECT * FROM `fs_user` WHERE  `username` = 'testuser' LIMIT 1 [ RunTime:0.000532s ]
[2025-07-27T00:35:01+08:00][sql] INSERT INTO `fs_user` SET `username` = 'testuser' , `password` = '$2y$10$fLTwhum3gQQMIZUSF2xPkOy5kZXjt9ppFn1.4cwHitNEBmhBXZsPG' , `nickname` = '测试用户' , `avatar` = 'user_avatar.jpg' , `email` = '<EMAIL>' , `phone` = '13800138000' , `gender` = 1 , `status` = 1 , `created_at` = '2025-07-27 00:35:01' , `updated_at` = '2025-07-27 00:35:01' [ RunTime:0.001438s ]
[2025-07-27T00:35:01+08:00][sql] SELECT * FROM `fs_user` WHERE  `username` = 'testuser' LIMIT 1 [ RunTime:0.000508s ]
[2025-07-27T00:44:44+08:00][sql] CONNECT:[ UseTime:0.002800s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-27T00:44:44+08:00][sql] SHOW FULL COLUMNS FROM `fs_admin` [ RunTime:0.001854s ]
[2025-07-27T00:44:44+08:00][sql] SELECT * FROM `fs_admin` WHERE  `id` = 1 LIMIT 1 [ RunTime:0.000753s ]
[2025-07-27T01:12:16+08:00][sql] CONNECT:[ UseTime:0.002415s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-27T01:12:16+08:00][sql] SHOW FULL COLUMNS FROM `fs_role` [ RunTime:0.001517s ]
[2025-07-27T01:12:16+08:00][sql] SELECT * FROM `fs_role` ORDER BY `id` ASC [ RunTime:0.000706s ]
[2025-07-27T01:21:40+08:00][sql] CONNECT:[ UseTime:0.016534s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-27T01:21:40+08:00][sql] DESCRIBE fs_role [ RunTime:0.001597s ]
[2025-07-27T01:22:08+08:00][error] [0]Call to undefined method think\Request::create()[D:\fanshop\server\test_role_create.php:26]
[2025-07-27T01:23:04+08:00][sql] CONNECT:[ UseTime:0.002715s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-27T01:23:04+08:00][sql] SHOW FULL COLUMNS FROM `fs_role` [ RunTime:0.001479s ]
[2025-07-27T01:23:04+08:00][sql] INSERT INTO `fs_role` SET `role_name` = '测试角色' , `role_code` = 'TEST_ROLE_1753550584' , `description` = '这是一个测试角色' , `status` = 1 , `created_at` = '2025-07-27 01:23:04.851219' , `updated_at` = '2025-07-27 01:23:04.851265' [ RunTime:0.002479s ]
[2025-07-27T01:25:38+08:00][sql] CONNECT:[ UseTime:0.002620s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-27T01:25:38+08:00][sql] SHOW FULL COLUMNS FROM `fs_role` [ RunTime:0.001511s ]
[2025-07-27T01:25:38+08:00][sql] SELECT * FROM `fs_role` [ RunTime:0.000548s ]
[2025-07-27T01:25:38+08:00][sql] SHOW FULL COLUMNS FROM `fs_admin_role` [ RunTime:0.001339s ]
[2025-07-27T01:25:38+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_admin_role` WHERE  `role_id` = 1 [ RunTime:0.000643s ]
[2025-07-27T01:25:38+08:00][sql] SELECT `a`.`username` FROM `fs_admin_role` `ar` INNER JOIN `fs_admin` `a` ON `ar`.`admin_id`=`a`.`id` WHERE  `ar`.`role_id` = '1' [ RunTime:0.000615s ]
[2025-07-27T01:25:38+08:00][error] [0]array_column(): Argument #1 ($array) must be of type array, think\Collection given[D:\fanshop\server\check_role_relations.php:31]
[2025-07-27T01:26:04+08:00][sql] CONNECT:[ UseTime:0.002534s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-27T01:26:04+08:00][sql] SHOW FULL COLUMNS FROM `fs_role` [ RunTime:0.001501s ]
[2025-07-27T01:26:04+08:00][sql] SELECT * FROM `fs_role` [ RunTime:0.000624s ]
[2025-07-27T01:26:04+08:00][sql] SHOW FULL COLUMNS FROM `fs_admin_role` [ RunTime:0.001264s ]
[2025-07-27T01:26:04+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_admin_role` WHERE  `role_id` = 1 [ RunTime:0.000702s ]
[2025-07-27T01:26:04+08:00][sql] SELECT `a`.`username` FROM `fs_admin_role` `ar` INNER JOIN `fs_admin` `a` ON `ar`.`admin_id`=`a`.`id` WHERE  `ar`.`role_id` = '1' [ RunTime:0.000707s ]
[2025-07-27T01:26:04+08:00][sql] SHOW FULL COLUMNS FROM `fs_role_menu` [ RunTime:0.001228s ]
[2025-07-27T01:26:04+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_role_menu` WHERE  `role_id` = 1 [ RunTime:0.000683s ]
[2025-07-27T01:26:04+08:00][sql] SHOW FULL COLUMNS FROM `fs_role_permission` [ RunTime:0.001242s ]
[2025-07-27T01:26:04+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_role_permission` WHERE  `role_id` = 1 [ RunTime:0.000690s ]
[2025-07-27T01:26:04+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_admin_role` WHERE  `role_id` = 2 [ RunTime:0.000611s ]
[2025-07-27T01:26:04+08:00][sql] SELECT `a`.`username` FROM `fs_admin_role` `ar` INNER JOIN `fs_admin` `a` ON `ar`.`admin_id`=`a`.`id` WHERE  `ar`.`role_id` = '2' [ RunTime:0.000797s ]
[2025-07-27T01:26:04+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_role_menu` WHERE  `role_id` = 2 [ RunTime:0.000636s ]
[2025-07-27T01:26:04+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_role_permission` WHERE  `role_id` = 2 [ RunTime:0.000664s ]
[2025-07-27T01:26:04+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_admin_role` WHERE  `role_id` = 3 [ RunTime:0.000592s ]
[2025-07-27T01:26:04+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_role_menu` WHERE  `role_id` = 3 [ RunTime:0.000637s ]
[2025-07-27T01:26:04+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_role_permission` WHERE  `role_id` = 3 [ RunTime:0.000650s ]
[2025-07-27T01:26:04+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_admin_role` WHERE  `role_id` = 4 [ RunTime:0.000611s ]
[2025-07-27T01:26:04+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_role_menu` WHERE  `role_id` = 4 [ RunTime:0.000720s ]
[2025-07-27T01:26:04+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_role_permission` WHERE  `role_id` = 4 [ RunTime:0.000528s ]
[2025-07-27T01:26:04+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_admin_role` WHERE  `role_id` = 5 [ RunTime:0.000480s ]
[2025-07-27T01:26:04+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_role_menu` WHERE  `role_id` = 5 [ RunTime:0.000439s ]
[2025-07-27T01:26:04+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_role_permission` WHERE  `role_id` = 5 [ RunTime:0.000473s ]
[2025-07-27T01:26:40+08:00][sql] CONNECT:[ UseTime:0.002557s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-27T01:26:40+08:00][sql] SHOW FULL COLUMNS FROM `fs_role` [ RunTime:0.001503s ]
[2025-07-27T01:26:40+08:00][sql] SELECT * FROM `fs_role` WHERE  `id` = 5 LIMIT 1 [ RunTime:0.000679s ]
[2025-07-27T01:26:40+08:00][sql] SHOW FULL COLUMNS FROM `fs_admin_role` [ RunTime:0.001267s ]
[2025-07-27T01:26:40+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_admin_role` WHERE  `role_id` = 5 [ RunTime:0.000592s ]
[2025-07-27T01:26:40+08:00][sql] SHOW FULL COLUMNS FROM `fs_role_menu` [ RunTime:0.001123s ]
[2025-07-27T01:26:40+08:00][sql] DELETE FROM `fs_role_menu` WHERE  `role_id` = 5 [ RunTime:0.000646s ]
[2025-07-27T01:26:40+08:00][sql] SHOW FULL COLUMNS FROM `fs_role_permission` [ RunTime:0.001209s ]
[2025-07-27T01:26:40+08:00][sql] DELETE FROM `fs_role_permission` WHERE  `role_id` = 5 [ RunTime:0.000614s ]
[2025-07-27T01:26:40+08:00][sql] DELETE FROM `fs_role` WHERE  `id` = 5 [ RunTime:0.001701s ]
[2025-07-27T09:20:49+08:00][sql] CONNECT:[ UseTime:0.002767s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-27T09:20:49+08:00][sql] SHOW FULL COLUMNS FROM `fs_menu` [ RunTime:0.003755s ]
[2025-07-27T09:20:49+08:00][sql] SELECT * FROM `fs_menu` [ RunTime:0.000661s ]
[2025-07-27T09:21:16+08:00][sql] CONNECT:[ UseTime:0.002441s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-27T09:21:16+08:00][sql] SHOW FULL COLUMNS FROM `fs_menu` [ RunTime:0.001538s ]
[2025-07-27T09:21:16+08:00][sql] SELECT * FROM `fs_menu` WHERE  `name` = 'Ecommerce' LIMIT 1 [ RunTime:0.000660s ]
[2025-07-27T09:21:16+08:00][sql] SHOW FULL COLUMNS FROM `fs_role_menu` [ RunTime:0.003327s ]
[2025-07-27T09:21:16+08:00][sql] DELETE FROM `fs_role_menu` WHERE  `menu_id` = 9 [ RunTime:0.000945s ]
[2025-07-27T09:21:16+08:00][sql] SHOW FULL COLUMNS FROM `fs_permission` [ RunTime:0.001127s ]
[2025-07-27T09:21:16+08:00][sql] SELECT * FROM `fs_permission` WHERE  `menu_id` = 9 [ RunTime:0.000398s ]
[2025-07-27T09:21:16+08:00][sql] DELETE FROM `fs_permission` WHERE  `menu_id` = 9 [ RunTime:0.000332s ]
[2025-07-27T09:21:16+08:00][sql] DELETE FROM `fs_menu` WHERE  `id` = 9 [ RunTime:0.001309s ]
[2025-07-27T09:21:16+08:00][sql] SELECT * FROM `fs_menu` WHERE  `name` = 'Analysis' LIMIT 1 [ RunTime:0.000445s ]
[2025-07-27T09:21:16+08:00][sql] SELECT * FROM `fs_menu` [ RunTime:0.000394s ]
[2025-07-27T09:37:27+08:00][sql] CONNECT:[ UseTime:0.002648s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-27T09:37:27+08:00][sql] SHOW FULL COLUMNS FROM `fs_user` [ RunTime:0.001496s ]
[2025-07-27T09:37:27+08:00][sql] SELECT * FROM `fs_user` LIMIT 5 [ RunTime:0.000503s ]
[2025-07-27T09:38:43+08:00][sql] CONNECT:[ UseTime:0.002449s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-27T09:38:43+08:00][sql] SHOW FULL COLUMNS FROM `fs_user` [ RunTime:0.001558s ]
[2025-07-27T09:38:43+08:00][sql] SELECT * FROM `fs_user` WHERE  `id` = 1 LIMIT 1 [ RunTime:0.000665s ]
