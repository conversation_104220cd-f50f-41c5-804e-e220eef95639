<?php
declare(strict_types=1);

namespace app\adminapi\validate;

use think\Validate;

/**
 * 会员验证器
 */
class MemberValidate extends Validate
{
    protected $rule = [
        'username' => 'require|alphaNum|length:3,50',
        'password' => 'require|length:6,32',
        'nickname' => 'max:100',
        'phone' => 'mobile',
        'email' => 'email',
        'parent_id' => 'integer|egt:0',
        'level' => 'in:1,2',
        'max_sub_accounts' => 'integer|egt:0',
        'expire_time' => 'dateFormat:Y-m-d H:i:s',
        'status' => 'in:0,1',
        'modules' => 'array',
    ];

    protected $message = [
        'username.require' => '用户名不能为空',
        'username.alphaNum' => '用户名只能包含字母和数字',
        'username.length' => '用户名长度必须在3-50个字符之间',
        'password.require' => '密码不能为空',
        'password.length' => '密码长度必须在6-32个字符之间',
        'nickname.max' => '昵称不能超过100个字符',
        'phone.mobile' => '手机号格式不正确',
        'email.email' => '邮箱格式不正确',
        'parent_id.integer' => '上级会员ID必须是整数',
        'parent_id.egt' => '上级会员ID不能小于0',
        'level.in' => '会员等级值错误',
        'max_sub_accounts.integer' => '最大子账号数必须是整数',
        'max_sub_accounts.egt' => '最大子账号数不能小于0',
        'expire_time.dateFormat' => '到期时间格式不正确',
        'status.in' => '状态值错误',
        'modules.array' => '模块数据格式错误',
    ];

    protected $scene = [
        'create' => ['username', 'password', 'nickname', 'phone', 'email', 'parent_id', 'level', 'max_sub_accounts', 'expire_time', 'status', 'modules'],
        'update' => ['nickname', 'phone', 'email', 'level', 'max_sub_accounts', 'expire_time', 'status', 'modules'],
        'login' => ['username', 'password'],
        'reset_password' => ['password'],
    ];

    /**
     * 自定义验证规则 - 验证模块数据格式
     */
    protected function checkModules($value, $rule, $data = [])
    {
        if (!is_array($value)) {
            return '模块数据必须是数组格式';
        }

        $availableModules = ['storage', 'sorting', 'order_entry', 'finance'];

        foreach ($value as $module) {
            if (!is_array($module)) {
                return '模块数据格式错误';
            }

            if (!isset($module['module_name']) || !in_array($module['module_name'], $availableModules)) {
                return '模块名称不正确';
            }

            if (isset($module['is_enabled']) && !in_array($module['is_enabled'], [0, 1])) {
                return '模块启用状态值错误';
            }

            if (isset($module['expire_time']) && !empty($module['expire_time'])) {
                if (!strtotime($module['expire_time'])) {
                    return '模块到期时间格式不正确';
                }
            }
        }

        return true;
    }

    /**
     * 自定义验证规则 - 验证上级会员
     */
    protected function checkParentId($value, $rule, $data = [])
    {
        if ($value == 0) {
            return true; // 0表示无上级
        }

        $parent = \app\common\model\Member::find($value);
        if (!$parent) {
            return '上级会员不存在';
        }

        if ($parent->status != 1) {
            return '上级会员已被禁用';
        }

        if ($parent->is_expired) {
            return '上级会员已到期';
        }

        return true;
    }

    /**
     * 自定义验证规则 - 验证到期时间
     */
    protected function checkExpireTime($value, $rule, $data = [])
    {
        if (empty($value)) {
            return true; // 允许为空，表示永久
        }

        $timestamp = strtotime($value);
        if (!$timestamp) {
            return '到期时间格式不正确';
        }

        if ($timestamp <= time()) {
            return '到期时间不能早于当前时间';
        }

        return true;
    }

    /**
     * 创建场景验证
     */
    public function sceneCreate()
    {
        return $this->only(['username', 'password', 'nickname', 'phone', 'email', 'parent_id', 'level', 'max_sub_accounts', 'expire_time', 'status'])
            ->append('parent_id', 'checkParentId')
            ->append('expire_time', 'checkExpireTime');
    }

    /**
     * 更新场景验证
     */
    public function sceneUpdate()
    {
        return $this->only(['nickname', 'phone', 'email', 'level', 'max_sub_accounts', 'expire_time', 'status'])
            ->append('expire_time', 'checkExpireTime');
    }

    /**
     * 登录场景验证
     */
    public function sceneLogin()
    {
        return $this->only(['username', 'password']);
    }

    /**
     * 重置密码场景验证
     */
    public function sceneResetPassword()
    {
        return $this->only(['password']);
    }

    /**
     * 设置模块场景验证
     */
    public function sceneSetModules()
    {
        return $this->only(['modules'])
            ->append('modules', 'checkModules');
    }
}
