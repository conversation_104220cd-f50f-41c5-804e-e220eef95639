<?php
declare(strict_types=1);

namespace app\adminapi\validate;

use think\Validate;

/**
 * 进货单验证器
 */
class PurchaseOrderValidate extends Validate
{
    protected $rule = [
        'order_no' => 'alphaNum|length:8,50',
        'supplier_name' => 'require|max:100',
        'supplier_contact' => 'max:100',
        'total_amount' => 'float|egt:0',
        'total_quantity' => 'integer|egt:0',
        'remark' => 'max:500',
        'items' => 'require|array|checkItems',
        'purchase_order_id' => 'require|integer|gt:0',
        'storage_items' => 'require|array|checkStorageItems',
        'adjust_quantity' => 'require|integer',
        'product_name' => 'require|max:200',
        'product_sku' => 'max:100',
    ];

    protected $message = [
        'order_no.alphaNum' => '进货单号只能包含字母和数字',
        'order_no.length' => '进货单号长度必须在8-50个字符之间',
        'supplier_name.require' => '供应商名称不能为空',
        'supplier_name.max' => '供应商名称不能超过100个字符',
        'supplier_contact.max' => '供应商联系方式不能超过100个字符',
        'total_amount.float' => '总金额必须是数字',
        'total_amount.egt' => '总金额不能小于0',
        'total_quantity.integer' => '总数量必须是整数',
        'total_quantity.egt' => '总数量不能小于0',
        'remark.max' => '备注不能超过500个字符',
        'items.require' => '商品明细不能为空',
        'items.array' => '商品明细格式错误',
        'purchase_order_id.require' => '进货单ID不能为空',
        'purchase_order_id.integer' => '进货单ID必须是整数',
        'purchase_order_id.gt' => '进货单ID必须大于0',
        'storage_items.require' => '入库商品不能为空',
        'storage_items.array' => '入库商品格式错误',
        'adjust_quantity.require' => '调整数量不能为空',
        'adjust_quantity.integer' => '调整数量必须是整数',
        'product_name.require' => '商品名称不能为空',
        'product_name.max' => '商品名称不能超过200个字符',
        'product_sku.max' => '商品SKU不能超过100个字符',
    ];

    protected $scene = [
        'create' => ['supplier_name', 'supplier_contact', 'remark', 'items'],
        'update' => ['supplier_name', 'supplier_contact', 'remark', 'items'],
        'storage' => ['purchase_order_id', 'items'],
        'adjust' => ['product_name', 'product_sku', 'adjust_quantity'],
    ];

    /**
     * 自定义验证规则 - 验证商品明细
     */
    protected function checkItems($value, $rule, $data = [])
    {
        if (!is_array($value) || empty($value)) {
            return '商品明细不能为空';
        }

        foreach ($value as $index => $item) {
            if (!is_array($item)) {
                return "第" . ($index + 1) . "个商品明细格式错误";
            }

            // 检查必填字段
            if (empty($item['product_name'])) {
                return "第" . ($index + 1) . "个商品名称不能为空";
            }

            if (!isset($item['purchase_price']) || !is_numeric($item['purchase_price']) || $item['purchase_price'] <= 0) {
                return "第" . ($index + 1) . "个商品进货价格必须大于0";
            }

            if (!isset($item['quantity']) || !is_numeric($item['quantity']) || $item['quantity'] <= 0) {
                return "第" . ($index + 1) . "个商品数量必须大于0";
            }

            // 检查字段长度
            if (strlen($item['product_name']) > 200) {
                return "第" . ($index + 1) . "个商品名称不能超过200个字符";
            }

            if (isset($item['product_sku']) && strlen($item['product_sku']) > 100) {
                return "第" . ($index + 1) . "个商品SKU不能超过100个字符";
            }

            if (isset($item['remark']) && strlen($item['remark']) > 500) {
                return "第" . ($index + 1) . "个商品备注不能超过500个字符";
            }
        }

        return true;
    }

    /**
     * 自定义验证规则 - 验证入库商品明细
     */
    protected function checkStorageItems($value, $rule, $data = [])
    {
        if (!is_array($value) || empty($value)) {
            return '入库商品明细不能为空';
        }

        foreach ($value as $index => $item) {
            if (!is_array($item)) {
                return "第" . ($index + 1) . "个入库商品格式错误";
            }

            // 检查必填字段
            if (!isset($item['item_id']) || !is_numeric($item['item_id']) || $item['item_id'] <= 0) {
                return "第" . ($index + 1) . "个商品明细ID无效";
            }

            if (!isset($item['storage_quantity']) || !is_numeric($item['storage_quantity']) || $item['storage_quantity'] <= 0) {
                return "第" . ($index + 1) . "个商品入库数量必须大于0";
            }
        }

        return true;
    }

    /**
     * 创建场景验证
     */
    public function sceneCreate()
    {
        return $this->only(['supplier_name', 'supplier_contact', 'remark', 'items'])
            ->append('items', 'checkItems');
    }

    /**
     * 更新场景验证
     */
    public function sceneUpdate()
    {
        return $this->only(['supplier_name', 'supplier_contact', 'remark', 'items'])
            ->append('items', 'checkItems');
    }

    /**
     * 入库场景验证
     */
    public function sceneStorage()
    {
        return $this->only(['purchase_order_id', 'items'])
            ->append('items', 'checkStorageItems');
    }

    /**
     * 库存调整场景验证
     */
    public function sceneAdjust()
    {
        return $this->only(['product_name', 'product_sku', 'adjust_quantity']);
    }
}
