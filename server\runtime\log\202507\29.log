[2025-07-29T10:14:22+08:00][sql] CONNECT:[ UseTime:0.002628s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-29T10:14:22+08:00][sql] SHOW FULL COLUMNS FROM `fs_menu` [ RunTime:0.002643s ]
[2025-07-29T10:14:22+08:00][sql] SELECT `m1`.`id`,`m1`.`name`,`m1`.`path`,`m1`.`component`,`m1`.`parent_id`,m2.name as parent_name FROM `fs_menu` `m1` LEFT JOIN `fs_menu` `m2` ON `m1`.`parent_id`=`m2`.`id` WHERE  `m1`.`name` IN ('文章管理','文章列表','文章分类','公告管理') OR `m1`.`path` LIKE '/system/article%' ORDER BY `m1`.`parent_id`,`m1`.`sort` [ RunTime:0.004431s ]
[2025-07-29T10:14:22+08:00][sql] UPDATE `fs_menu`  SET `component` = '/index/index'  WHERE  `name` = '文章管理'  AND `path` = '/system/article'  AND `component` IN ('system/article/index','system/article/index.vue') [ RunTime:0.000646s ]
[2025-07-29T10:14:22+08:00][sql] UPDATE `fs_menu`  SET `component` = '/views/system/article/list/index'  WHERE  `name` = '文章列表'  AND `path` = '/system/article/list'  AND `component` IN ('system/article/list/index','system/article/list/index.vue') [ RunTime:0.000831s ]
[2025-07-29T10:14:22+08:00][sql] UPDATE `fs_menu`  SET `component` = '/views/system/article/category/index'  WHERE  `name` = '文章分类'  AND `path` = '/system/article/category'  AND `component` IN ('system/article/category/index','system/article/category/index.vue') [ RunTime:0.000792s ]
[2025-07-29T10:14:22+08:00][sql] UPDATE `fs_menu`  SET `component` = '/views/system/article/notice/index'  WHERE  `name` = '公告管理'  AND `path` = '/system/article/notice'  AND `component` IN ('system/article/notice/index','system/article/notice/index.vue') [ RunTime:0.000674s ]
[2025-07-29T10:15:34+08:00][sql] CONNECT:[ UseTime:0.018014s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-29T10:15:34+08:00][sql] SHOW FULL COLUMNS FROM `fs_menu` [ RunTime:0.001813s ]
[2025-07-29T10:15:34+08:00][sql] SELECT `m1`.`id`,`m1`.`name`,`m1`.`path`,`m1`.`component`,`m1`.`parent_id`,m2.name as parent_name FROM `fs_menu` `m1` LEFT JOIN `fs_menu` `m2` ON `m1`.`parent_id`=`m2`.`id` WHERE  `m1`.`name` IN ('文章管理','文章列表','文章分类','公告管理') OR `m1`.`path` LIKE '/system/article%' ORDER BY `m1`.`parent_id`,`m1`.`sort` [ RunTime:0.003296s ]
[2025-07-29T10:15:34+08:00][sql] UPDATE `fs_menu`  SET `component` = '/views/system/article/list/index'  WHERE  `name` = 'article-list'  AND `path` = '/system/article/list'  AND `component` = '/system/article/list' [ RunTime:0.002046s ]
[2025-07-29T10:15:34+08:00][sql] UPDATE `fs_menu`  SET `component` = '/views/system/article/category/index'  WHERE  `name` = 'category'  AND `path` = '/system/article/category'  AND `component` = '/system/article/category/index' [ RunTime:0.001768s ]
[2025-07-29T10:15:34+08:00][sql] UPDATE `fs_menu`  SET `component` = '/views/system/article/notice/index'  WHERE  `name` = 'notice'  AND `path` = '/system/article/notice'  AND `component` = '/system/article/notice/index' [ RunTime:0.001924s ]
[2025-07-29T10:15:34+08:00][sql] SELECT `m1`.`id`,`m1`.`name`,`m1`.`path`,`m1`.`component`,`m1`.`parent_id`,m2.name as parent_name FROM `fs_menu` `m1` LEFT JOIN `fs_menu` `m2` ON `m1`.`parent_id`=`m2`.`id` WHERE  `m1`.`name` IN ('System','article','article-list','category','notice') OR `m1`.`id` IN (SELECT `parent_id` FROM `fs_menu` WHERE  `name` IN ('article','article-list','category','notice')) ORDER BY `m1`.`parent_id`,`m1`.`sort` [ RunTime:0.003563s ]
[2025-07-29T10:17:09+08:00][sql] CONNECT:[ UseTime:0.003088s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-29T10:17:09+08:00][sql] SHOW FULL COLUMNS FROM `fs_menu` [ RunTime:0.001717s ]
[2025-07-29T10:17:09+08:00][sql] UPDATE `fs_menu`  SET `component` = '/system/article/list/index'  WHERE  `name` = 'article-list' [ RunTime:0.001950s ]
[2025-07-29T10:17:09+08:00][sql] UPDATE `fs_menu`  SET `component` = '/system/article/category/index'  WHERE  `name` = 'category' [ RunTime:0.001752s ]
[2025-07-29T10:17:09+08:00][sql] UPDATE `fs_menu`  SET `component` = '/system/article/notice/index'  WHERE  `name` = 'notice' [ RunTime:0.001767s ]
[2025-07-29T10:17:09+08:00][sql] SELECT `name`,`path`,`component` FROM `fs_menu` WHERE  `name` IN ('article-list','category','notice') [ RunTime:0.000735s ]
[2025-07-29T10:21:44+08:00][sql] CONNECT:[ UseTime:0.023078s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-29T10:21:44+08:00][sql] SHOW FULL COLUMNS FROM `fs_menu` [ RunTime:0.001238s ]
[2025-07-29T10:21:44+08:00][sql] SELECT `m1`.`id`,`m1`.`name`,`m1`.`path`,`m1`.`component`,`m1`.`parent_id`,m2.name as parent_name FROM `fs_menu` `m1` LEFT JOIN `fs_menu` `m2` ON `m1`.`parent_id`=`m2`.`id` WHERE  `m1`.`path` LIKE '/system%' ORDER BY `m1`.`parent_id`,`m1`.`sort` [ RunTime:0.002022s ]
[2025-07-29T10:21:44+08:00][sql] SELECT `id`,`name`,`path`,`component` FROM `fs_menu` WHERE  `component` LIKE '/system/article%' OR `component` LIKE 'system/article%' [ RunTime:0.000499s ]
[2025-07-29T10:22:20+08:00][sql] CONNECT:[ UseTime:0.015623s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-29T10:22:20+08:00][sql] SHOW FULL COLUMNS FROM `fs_menu` [ RunTime:0.001827s ]
[2025-07-29T10:22:20+08:00][sql] UPDATE `fs_menu`  SET `component` = 'system/article/list/index'  WHERE  `name` = 'article-list'  AND `path` = '/system/article/list' [ RunTime:0.003191s ]
[2025-07-29T10:22:20+08:00][sql] UPDATE `fs_menu`  SET `component` = 'system/article/category/index'  WHERE  `name` = 'category'  AND `path` = '/system/article/category' [ RunTime:0.002180s ]
[2025-07-29T10:22:20+08:00][sql] UPDATE `fs_menu`  SET `component` = 'system/article/notice/index'  WHERE  `name` = 'notice'  AND `path` = '/system/article/notice' [ RunTime:0.005153s ]
[2025-07-29T10:22:20+08:00][sql] SELECT `name`,`path`,`component` FROM `fs_menu` WHERE  `name` IN ('article-list','category','notice') [ RunTime:0.000582s ]
[2025-07-29T10:28:11+08:00][sql] CONNECT:[ UseTime:0.002560s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-29T10:28:11+08:00][sql] SHOW FULL COLUMNS FROM `fs_menu` [ RunTime:0.001666s ]
[2025-07-29T10:28:11+08:00][sql] SELECT `id`,`name`,`path`,`component` FROM `fs_menu` WHERE  `name` IN ('article-list','category','notice') [ RunTime:0.000812s ]
[2025-07-29T10:28:11+08:00][sql] UPDATE `fs_menu`  SET `component` = '/system/article/list/index'  WHERE  `name` = 'article-list' [ RunTime:0.002119s ]
[2025-07-29T10:28:11+08:00][sql] UPDATE `fs_menu`  SET `component` = '/system/article/category/index'  WHERE  `name` = 'category' [ RunTime:0.001749s ]
[2025-07-29T10:28:11+08:00][sql] UPDATE `fs_menu`  SET `component` = '/system/article/notice/index'  WHERE  `name` = 'notice' [ RunTime:0.001832s ]
[2025-07-29T10:28:11+08:00][sql] SELECT `name`,`path`,`component` FROM `fs_menu` WHERE  `name` IN ('article-list','category','notice') [ RunTime:0.000799s ]
[2025-07-29T10:32:43+08:00][sql] CONNECT:[ UseTime:0.002595s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-29T10:32:43+08:00][sql] SHOW FULL COLUMNS FROM `fs_menu` [ RunTime:0.001147s ]
[2025-07-29T10:32:43+08:00][sql] SELECT `id`,`name`,`path`,`component` FROM `fs_menu` WHERE  `name` = 'article'  AND `path` = '/system/article' LIMIT 1 [ RunTime:0.000565s ]
[2025-07-29T10:32:43+08:00][sql] UPDATE `fs_menu`  SET `component` = ''  WHERE  `name` = 'article'  AND `path` = '/system/article' [ RunTime:0.001976s ]
[2025-07-29T10:32:43+08:00][sql] SELECT `m1`.`id`,`m1`.`name`,`m1`.`path`,`m1`.`component`,m2.name as parent_name FROM `fs_menu` `m1` LEFT JOIN `fs_menu` `m2` ON `m1`.`parent_id`=`m2`.`id` WHERE  `m1`.`path` LIKE '/system/article%' ORDER BY `m1`.`parent_id`,`m1`.`sort` [ RunTime:0.000639s ]
[2025-07-29T10:52:29+08:00][sql] CONNECT:[ UseTime:0.002723s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-29T10:52:29+08:00][sql] SHOW FULL COLUMNS FROM `fs_menu` [ RunTime:0.001642s ]
[2025-07-29T10:52:29+08:00][sql] SELECT `id`,`name`,`path`,`component`,`parent_id` FROM `fs_menu` WHERE  `path` LIKE '/system/article%' ORDER BY `parent_id`,`sort` [ RunTime:0.000927s ]
[2025-07-29T10:53:00+08:00][sql] CONNECT:[ UseTime:0.002580s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-29T10:53:00+08:00][sql] SHOW FULL COLUMNS FROM `fs_menu` [ RunTime:0.000969s ]
[2025-07-29T10:53:00+08:00][sql] SELECT `id`,`name`,`path`,`component` FROM `fs_menu` WHERE  `id` = 12 LIMIT 1 [ RunTime:0.000595s ]
[2025-07-29T10:53:00+08:00][sql] UPDATE `fs_menu`  SET `component` = ''  WHERE  `id` = 12 [ RunTime:0.001727s ]
[2025-07-29T10:53:00+08:00][sql] SELECT `id`,`name`,`path`,`component`,`parent_id` FROM `fs_menu` WHERE  `path` LIKE '/system/article%' ORDER BY `parent_id`,`sort` [ RunTime:0.000894s ]
[2025-07-29T10:55:24+08:00][sql] CONNECT:[ UseTime:0.002626s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-29T10:55:24+08:00][sql] SHOW FULL COLUMNS FROM `fs_menu` [ RunTime:0.001318s ]
[2025-07-29T10:55:24+08:00][sql] SELECT `id`,`name`,`path`,`component` FROM `fs_menu` WHERE  `name` = 'article'  AND `path` = '/system/article' LIMIT 1 [ RunTime:0.000762s ]
[2025-07-29T10:55:24+08:00][sql] UPDATE `fs_menu`  SET `component` = '/common/router-view/index'  WHERE  `name` = 'article'  AND `path` = '/system/article' [ RunTime:0.001620s ]
[2025-07-29T10:55:24+08:00][sql] SELECT `id`,`name`,`path`,`component`,`parent_id` FROM `fs_menu` WHERE  `path` LIKE '/system/article%' ORDER BY `parent_id`,`sort` [ RunTime:0.000647s ]
[2025-07-29T11:07:00+08:00][sql] CONNECT:[ UseTime:0.002701s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-29T11:07:00+08:00][sql] SHOW FULL COLUMNS FROM `fs_menu` [ RunTime:0.001632s ]
[2025-07-29T11:07:00+08:00][sql] SELECT `id`,`name`,`path`,`component` FROM `fs_menu` WHERE  `name` = 'article'  AND `path` = '/system/article' LIMIT 1 [ RunTime:0.000874s ]
[2025-07-29T11:07:00+08:00][sql] UPDATE `fs_menu`  SET `component` = '/index/index'  WHERE  `name` = 'article'  AND `path` = '/system/article' [ RunTime:0.002378s ]
[2025-07-29T11:07:00+08:00][sql] SELECT `id`,`name`,`path`,`component`,`parent_id` FROM `fs_menu` WHERE  `path` LIKE '/system/article%' ORDER BY `parent_id`,`sort` [ RunTime:0.000817s ]
[2025-07-29T11:10:58+08:00][sql] CONNECT:[ UseTime:0.017328s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-29T11:10:58+08:00][sql] SHOW FULL COLUMNS FROM `fs_menu` [ RunTime:0.001658s ]
[2025-07-29T11:10:58+08:00][sql] SELECT `id`,`name`,`path`,`component` FROM `fs_menu` WHERE  `name` = 'article'  AND `path` = '/system/article' LIMIT 1 [ RunTime:0.000929s ]
[2025-07-29T11:10:58+08:00][sql] UPDATE `fs_menu`  SET `component` = ''  WHERE  `name` = 'article'  AND `path` = '/system/article' [ RunTime:0.001969s ]
[2025-07-29T11:10:58+08:00][sql] SELECT `id`,`name`,`path`,`component`,`parent_id` FROM `fs_menu` WHERE  `path` LIKE '/system/article%' ORDER BY `parent_id`,`sort` [ RunTime:0.000787s ]
[2025-07-29T11:13:02+08:00][sql] CONNECT:[ UseTime:0.002235s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-29T11:13:02+08:00][sql] SHOW FULL COLUMNS FROM `fs_menu` [ RunTime:0.001048s ]
[2025-07-29T11:13:02+08:00][sql] SELECT `id`,`name`,`path`,`component`,`parent_id` FROM `fs_menu` WHERE  `path` LIKE '/system/article%' ORDER BY `parent_id`,`sort` [ RunTime:0.000585s ]
[2025-07-29T11:13:02+08:00][sql] SELECT * FROM `fs_menu` WHERE  `path` LIKE '/system/article%' ORDER BY `parent_id`,`sort` [ RunTime:0.000623s ]
[2025-07-29T11:20:07+08:00][sql] CONNECT:[ UseTime:0.002361s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-29T11:20:07+08:00][sql] SHOW TABLES LIKE 'fs_categories' [ RunTime:0.000840s ]
[2025-07-29T11:20:07+08:00][sql] SHOW FULL COLUMNS FROM `fs_categories` [ RunTime:0.001663s ]
[2025-07-29T11:20:07+08:00][sql] SELECT * FROM `fs_categories` [ RunTime:0.000595s ]
[2025-07-29T11:23:08+08:00][sql] CONNECT:[ UseTime:0.002856s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-29T11:23:08+08:00][sql] SHOW FULL COLUMNS FROM `fs_articles` [ RunTime:0.001946s ]
[2025-07-29T11:23:08+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_articles` [ RunTime:0.000745s ]
[2025-07-29T11:23:08+08:00][sql] SELECT * FROM `fs_articles` ORDER BY `sort` DESC,`id` DESC LIMIT 0,10 [ RunTime:0.000866s ]
[2025-07-29T11:23:08+08:00][sql] SELECT * FROM `fs_articles` WHERE  `slug` = 'test-article-1753759388' LIMIT 1 [ RunTime:0.000826s ]
[2025-07-29T11:23:08+08:00][sql] INSERT INTO `fs_articles` SET `title` = '测试文章标题' , `slug` = 'test-article-1753759388' , `content` = '<p>这是一篇测试文章的内容，包含<strong>富文本</strong>格式。</p>' , `summary` = '这是文章摘要' , `category_id` = 1 , `tags` = '测试,文章,富文本' , `author_name` = '测试作者' , `is_published` = 0 , `is_featured` = 0 , `status` = 1 , `sort` = 0 [ RunTime:0.000859s ]
[2025-07-29T11:23:08+08:00][sql] SELECT * FROM `fs_articles` WHERE  `id` = 10 LIMIT 1 [ RunTime:0.000812s ]
[2025-07-29T11:23:08+08:00][sql] SELECT * FROM `fs_articles` WHERE  `id` = 10 LIMIT 1 [ RunTime:0.000811s ]
[2025-07-29T11:23:08+08:00][sql] SELECT * FROM `fs_articles` WHERE  `id` = 10 LIMIT 1 [ RunTime:0.000872s ]
[2025-07-29T11:23:08+08:00][sql] SELECT * FROM `fs_articles` WHERE  `id` = 10 LIMIT 1 [ RunTime:0.000708s ]
[2025-07-29T11:23:08+08:00][sql] UPDATE `fs_articles`  SET `title` = '更新后的文章标题' , `content` = '<p>这是更新后的文章内容，包含<em>更多</em>的<strong>富文本</strong>格式。</p><ul><li>列表项1</li><li>列表项2</li></ul>' , `is_published` = 1  WHERE  `id` = 10 [ RunTime:0.000886s ]
[2025-07-29T11:23:08+08:00][sql] SELECT * FROM `fs_articles` WHERE  `id` = 10 LIMIT 1 [ RunTime:0.000842s ]
[2025-07-29T11:23:08+08:00][sql] SELECT * FROM `fs_articles` WHERE  `id` = 10 LIMIT 1 [ RunTime:0.000607s ]
[2025-07-29T11:23:08+08:00][sql] DELETE FROM `fs_articles` WHERE  `id` = 10 [ RunTime:0.000524s ]
[2025-07-29T13:38:01+08:00][sql] CONNECT:[ UseTime:0.016875s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-29T13:38:01+08:00][sql] SHOW FULL COLUMNS FROM `fs_menu` [ RunTime:0.001207s ]
[2025-07-29T13:38:01+08:00][sql] SELECT * FROM `fs_menu` WHERE  `path` LIKE '/system/article%' ORDER BY `parent_id`,`sort` [ RunTime:0.000898s ]
[2025-07-29T13:38:01+08:00][sql] SELECT * FROM `fs_menu` WHERE  `path` = '/system/article/list' LIMIT 1 [ RunTime:0.000597s ]
[2025-07-29T13:38:01+08:00][sql] SELECT * FROM `fs_menu` WHERE  `path` = '/system/article/list/add' LIMIT 1 [ RunTime:0.000530s ]
[2025-07-29T13:38:01+08:00][sql] INSERT INTO `fs_menu` SET `parent_id` = 13 , `name` = 'article-add' , `path` = '/system/article/list/add' , `component` = '/system/article/list/add' , `title` = 'Add Article' , `icon` = '' , `sort` = 1 , `type` = 1 , `status` = 1 , `keep_alive` = 0 , `external_link` = '' , `is_iframe` = 0 , `created_at` = '2025-07-29 13:38:01' , `updated_at` = '2025-07-29 13:38:01' [ RunTime:0.001688s ]
[2025-07-29T13:38:01+08:00][sql] SELECT * FROM `fs_menu` WHERE  `path` LIKE '/system/article%' ORDER BY `parent_id`,`sort` [ RunTime:0.000631s ]
[2025-07-29T13:42:57+08:00][sql] CONNECT:[ UseTime:0.002676s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-29T13:42:57+08:00][sql] SHOW FULL COLUMNS FROM `fs_menu` [ RunTime:0.001694s ]
[2025-07-29T13:42:57+08:00][sql] UPDATE `fs_menu`  SET `path` = '/system/article/list/edit' , `component` = '/system/article/list/edit' , `title` = 'Edit Article' , `updated_at` = '2025-07-29 13:42:57'  WHERE  `path` = '/system/article/list/add' [ RunTime:0.000883s ]
[2025-07-29T13:42:57+08:00][sql] SELECT * FROM `fs_menu` WHERE  `path` LIKE '/system/article%' ORDER BY `parent_id`,`sort` [ RunTime:0.000940s ]
[2025-07-29T13:44:42+08:00][sql] CONNECT:[ UseTime:0.002391s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-29T13:44:42+08:00][sql] SHOW FULL COLUMNS FROM `fs_menu` [ RunTime:0.001192s ]
[2025-07-29T13:44:42+08:00][sql] DELETE FROM `fs_menu` WHERE  `path` = '/system/article/list/edit' [ RunTime:0.000560s ]
[2025-07-29T13:44:42+08:00][sql] DELETE FROM `fs_menu` WHERE  `path` = '/system/article/list/add' [ RunTime:0.000467s ]
[2025-07-29T13:44:42+08:00][sql] SELECT * FROM `fs_menu` WHERE  `path` LIKE '/system/article%' ORDER BY `parent_id`,`sort` [ RunTime:0.000810s ]
[2025-07-29T18:48:12+08:00][sql] CONNECT:[ UseTime:0.002807s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-29T18:48:12+08:00][sql] SHOW FULL COLUMNS FROM `fs_system_config` [ RunTime:0.001636s ]
[2025-07-29T18:48:12+08:00][sql] SELECT * FROM `fs_system_config` WHERE  `config_key` = 'file_url_full' LIMIT 1 [ RunTime:0.000889s ]
[2025-07-29T18:48:12+08:00][sql] INSERT INTO `fs_system_config` SET `config_key` = 'file_url_full' , `config_value` = '' , `config_desc` = '文件URL格式：false=相对路径，true=完整域名' , `config_type` = 'boolean' [ RunTime:0.001924s ]
[2025-07-29T18:48:12+08:00][sql] SELECT * FROM `fs_system_config` WHERE  `config_key` = 'file_url_full' LIMIT 1 [ RunTime:0.000829s ]
[2025-07-29T19:25:01+08:00][sql] CONNECT:[ UseTime:0.002736s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-29T19:25:01+08:00][sql] SHOW FULL COLUMNS FROM `fs_upload` [ RunTime:0.001652s ]
[2025-07-29T19:25:01+08:00][sql] SELECT * FROM `fs_upload` WHERE  `file_path` LIKE '%\\%' [ RunTime:0.000900s ]
[2025-07-29T19:25:01+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_upload` WHERE  `file_path` LIKE '%\\%' [ RunTime:0.000613s ]
[2025-07-29T19:26:18+08:00][sql] CONNECT:[ UseTime:0.022123s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-29T19:26:18+08:00][sql] SHOW FULL COLUMNS FROM `fs_upload` [ RunTime:0.001684s ]
[2025-07-29T19:26:18+08:00][sql] SELECT * FROM `fs_upload` ORDER BY `id` DESC LIMIT 10 [ RunTime:0.000830s ]
