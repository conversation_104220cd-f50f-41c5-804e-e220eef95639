<?php
// 数据库连接测试脚本
require_once 'vendor/autoload.php';

use think\facade\Db;
use think\facade\Config;

try {
    // 初始化ThinkPHP
    $app = new \think\App();
    $app->initialize();
    
    echo "正在测试数据库连接...\n";
    
    // 测试数据库连接
    $connection = Db::connect();
    echo "✓ 数据库连接成功\n";
    
    // 检查表是否存在
    $tables = [
        'fs_members' => '会员表',
        'fs_member_modules' => '会员模块权限表',
        'fs_purchase_orders' => '进货单表',
        'fs_purchase_order_items' => '进货单商品明细表',
        'fs_customer_orders' => '客户订单表',
        'fs_customer_order_items' => '客户订单商品明细表',
        'fs_inventory' => '库存表',
        'fs_financial_records' => '财务记录表'
    ];
    
    echo "\n检查表结构:\n";
    $allTablesExist = true;
    
    foreach ($tables as $tableName => $description) {
        try {
            $exists = Db::query("SHOW TABLES LIKE '{$tableName}'");
            if (!empty($exists)) {
                echo "✓ {$tableName} ({$description}) - 存在\n";
            } else {
                echo "✗ {$tableName} ({$description}) - 不存在\n";
                $allTablesExist = false;
            }
        } catch (Exception $e) {
            echo "✗ {$tableName} ({$description}) - 检查失败: " . $e->getMessage() . "\n";
            $allTablesExist = false;
        }
    }
    
    if ($allTablesExist) {
        echo "\n检查默认数据:\n";
        
        // 检查管理员账号
        try {
            $admin = Db::table('fs_members')->where('username', 'admin')->find();
            if ($admin) {
                echo "✓ 默认管理员账号存在 (ID: {$admin['id']})\n";
                
                // 检查管理员模块权限
                $modules = Db::table('fs_member_modules')->where('member_id', $admin['id'])->select();
                echo "✓ 管理员模块权限: " . count($modules) . " 个模块\n";
                foreach ($modules as $module) {
                    echo "  - {$module['module_name']}: {$module['module_title']}\n";
                }
            } else {
                echo "✗ 默认管理员账号不存在\n";
            }
        } catch (Exception $e) {
            echo "✗ 检查默认数据失败: " . $e->getMessage() . "\n";
        }
        
        echo "\n🎉 数据库迁移检查完成！所有表都已创建。\n";
    } else {
        echo "\n❌ 数据库迁移未完成，请手动执行SQL文件。\n";
        echo "请在数据库管理工具中执行: server/database/migrations/create_ai_sorting_tables.sql\n";
    }
    
} catch (Exception $e) {
    echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
    echo "请检查 .env 文件中的数据库配置\n";
}
