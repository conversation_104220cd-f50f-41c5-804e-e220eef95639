# AI分拣系统开发方案

## 📋 项目概述

基于现有的 **Vue3 + TypeScript + ThinkPHP8** 架构，开发一个完整的AI分拣系统，实现会员管理、入库、分拣、录单、财务等核心功能。

### 🎯 核心功能模块
1. **会员管理系统** - 多级会员权限，模块功能控制
2. **入库管理** - 进货单管理，入库操作
3. **分拣系统** - 订单分配，分拣大厅
4. **录单功能** - 客户订单录入，价格管理
5. **财务系统** - 收益统计，欠款管理

---

## 🏗️ 系统架构设计

### 用户角色体系
```
后台超级管理员 (fs_admin)
└── AI分拣主会员 (fs_user) - 后台管理员添加
    └── 子账号 (fs_user_sub_accounts) - 会员在移动端创建
        ├── 入库员子账号 (入库功能模块)
        ├── 录单员子账号 (录单功能模块)
        ├── 分拣员子账号 (分拣功能模块)
        └── 财务员子账号 (财务功能模块)
```

### 用户体系说明
- **后台管理员** (`fs_admin`): 系统超级管理员，负责添加和管理AI分拣主会员
- **AI分拣主会员** (`fs_user`): 由后台管理员添加的会员，拥有完整的AI分拣系统权限
- **子账号** (`fs_user_sub_accounts`): 由主会员在移动端创建的子账号，每个子账号分配特定的功能模块权限

### 业务流程图
```
进货单生成 → 入库操作 → 客户录单 → 管理员定价 → 订单分配 → 分拣操作 → 财务结算
     ↑                                        ↓
   管理员                                  分拣大厅
```

---

## 📊 数据库设计

### 1. 会员管理相关表

#### 会员表 (fs_members)
```sql
CREATE TABLE `fs_members` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `parent_id` int(11) DEFAULT 0 COMMENT '上级会员ID，0为管理员开通',
  `member_type` tinyint(1) DEFAULT 1 COMMENT '会员类型：1=普通会员，2=管理员开通',
  `expire_time` datetime DEFAULT NULL COMMENT '到期时间',
  `max_sub_accounts` int(11) DEFAULT 0 COMMENT '最大子账号数量',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1=正常，0=禁用',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员表';
```

#### 会员模块权限表 (fs_member_modules)
```sql
CREATE TABLE `fs_member_modules` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `member_id` int(11) NOT NULL COMMENT '会员ID',
  `module_code` varchar(50) NOT NULL COMMENT '模块代码：storage,sorting,order_entry,finance',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1=开通，0=关闭',
  `expire_time` datetime DEFAULT NULL COMMENT '模块到期时间',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `member_module` (`member_id`, `module_code`),
  KEY `idx_member_id` (`member_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员模块权限表';
```

### 2. 业务核心表

#### 进货单表 (fs_purchase_orders)
```sql
CREATE TABLE `fs_purchase_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_no` varchar(50) NOT NULL COMMENT '进货单号',
  `supplier_name` varchar(100) NOT NULL COMMENT '供应商名称',
  `total_amount` decimal(10,2) DEFAULT 0.00 COMMENT '总金额',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1=待入库，2=已入库，3=已完成',
  `created_by` int(11) NOT NULL COMMENT '创建人ID',
  `storage_by` int(11) DEFAULT NULL COMMENT '入库员ID',
  `storage_time` datetime DEFAULT NULL COMMENT '入库时间',
  `remark` text COMMENT '备注',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `idx_status` (`status`),
  KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='进货单表';
```

#### 进货单明细表 (fs_purchase_order_items)
```sql
CREATE TABLE `fs_purchase_order_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `purchase_order_id` int(11) NOT NULL COMMENT '进货单ID',
  `product_name` varchar(200) NOT NULL COMMENT '商品名称',
  `product_code` varchar(100) DEFAULT NULL COMMENT '商品编码',
  `specification` varchar(100) DEFAULT NULL COMMENT '规格',
  `unit` varchar(20) DEFAULT NULL COMMENT '单位',
  `quantity` decimal(10,2) NOT NULL COMMENT '数量',
  `purchase_price` decimal(10,2) DEFAULT 0.00 COMMENT '进货价',
  `total_price` decimal(10,2) DEFAULT 0.00 COMMENT '小计',
  `storage_quantity` decimal(10,2) DEFAULT 0.00 COMMENT '已入库数量',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_purchase_order_id` (`purchase_order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='进货单明细表';
```

#### 客户订单表 (fs_customer_orders)
```sql
CREATE TABLE `fs_customer_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_no` varchar(50) NOT NULL COMMENT '订单号',
  `customer_name` varchar(100) NOT NULL COMMENT '客户名称',
  `customer_phone` varchar(20) DEFAULT NULL COMMENT '客户电话',
  `customer_address` text COMMENT '客户地址',
  `total_amount` decimal(10,2) DEFAULT 0.00 COMMENT '订单总额',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1=待定价，2=待分配，3=分拣中，4=已完成',
  `entry_by` int(11) NOT NULL COMMENT '录单员ID',
  `price_by` int(11) DEFAULT NULL COMMENT '定价员ID',
  `assign_to` int(11) DEFAULT NULL COMMENT '分配给分拣员ID',
  `sorting_by` int(11) DEFAULT NULL COMMENT '实际分拣员ID',
  `entry_time` datetime DEFAULT NULL COMMENT '录单时间',
  `price_time` datetime DEFAULT NULL COMMENT '定价时间',
  `assign_time` datetime DEFAULT NULL COMMENT '分配时间',
  `sorting_time` datetime DEFAULT NULL COMMENT '分拣时间',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `remark` text COMMENT '备注',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `idx_status` (`status`),
  KEY `idx_entry_by` (`entry_by`),
  KEY `idx_assign_to` (`assign_to`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户订单表';
```

#### 订单明细表 (fs_customer_order_items)
```sql
CREATE TABLE `fs_customer_order_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL COMMENT '订单ID',
  `product_name` varchar(200) NOT NULL COMMENT '商品名称',
  `product_code` varchar(100) DEFAULT NULL COMMENT '商品编码',
  `specification` varchar(100) DEFAULT NULL COMMENT '规格',
  `unit` varchar(20) DEFAULT NULL COMMENT '单位',
  `quantity` decimal(10,2) NOT NULL COMMENT '数量',
  `unit_price` decimal(10,2) DEFAULT 0.00 COMMENT '单价',
  `total_price` decimal(10,2) DEFAULT 0.00 COMMENT '小计',
  `sorting_quantity` decimal(10,2) DEFAULT 0.00 COMMENT '已分拣数量',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单明细表';
```

#### 财务记录表 (fs_financial_records)
```sql
CREATE TABLE `fs_financial_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `record_type` tinyint(1) NOT NULL COMMENT '记录类型：1=收入，2=支出',
  `business_type` varchar(50) NOT NULL COMMENT '业务类型：order_income,purchase_cost等',
  `business_id` int(11) DEFAULT NULL COMMENT '关联业务ID',
  `amount` decimal(10,2) NOT NULL COMMENT '金额',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `operator_id` int(11) NOT NULL COMMENT '操作员ID',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_record_type` (`record_type`),
  KEY `idx_business_type` (`business_type`),
  KEY `idx_operator_id` (`operator_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='财务记录表';
```

---

## 🎯 功能模块设计

### 1. 会员管理模块

#### 功能特性
- **多级会员体系** - 支持管理员开通会员，会员开通子账号
- **模块权限控制** - 精确控制每个会员的功能模块权限
- **到期时间管理** - 会员和模块的到期时间控制
- **人数限制** - 控制会员可开通的子账号数量

#### 核心接口
```typescript
// 会员管理API
export interface MemberInfo {
  id: number
  username: string
  nickname: string
  parent_id: number
  member_type: number
  expire_time: string
  max_sub_accounts: number
  current_sub_accounts: number
  modules: ModuleInfo[]
  status: number
}

export interface ModuleInfo {
  module_code: string
  module_name: string
  status: number
  expire_time: string
}

export class MemberService {
  // 获取会员列表
  static async getMemberList(params: any): Promise<any>
  
  // 创建会员
  static async createMember(data: MemberFormData): Promise<MemberInfo>
  
  // 开通模块
  static async enableModule(memberId: number, moduleCode: string, expireTime: string): Promise<void>
  
  // 创建子账号
  static async createSubAccount(data: SubAccountFormData): Promise<MemberInfo>
}
```

### 2. 入库管理模块

#### 功能特性
- **进货单管理** - 管理员创建进货单
- **入库操作** - 入库员按单入库
- **库存跟踪** - 实时库存数据

#### 核心接口
```typescript
export interface PurchaseOrderInfo {
  id: number
  order_no: string
  supplier_name: string
  total_amount: number
  status: number
  items: PurchaseOrderItemInfo[]
  created_at: string
}

export class StorageService {
  // 获取进货单列表
  static async getPurchaseOrderList(params: any): Promise<any>
  
  // 创建进货单
  static async createPurchaseOrder(data: PurchaseOrderFormData): Promise<PurchaseOrderInfo>
  
  // 入库操作
  static async storageOperation(orderId: number, items: StorageItemData[]): Promise<void>
}
```

### 3. 录单管理模块

#### 功能特性
- **客户订单录入** - 录单员录入客户订单
- **订单管理** - 订单状态跟踪
- **客户信息管理** - 客户资料维护

#### 核心接口
```typescript
export interface CustomerOrderInfo {
  id: number
  order_no: string
  customer_name: string
  customer_phone: string
  total_amount: number
  status: number
  items: CustomerOrderItemInfo[]
  created_at: string
}

export class OrderEntryService {
  // 获取订单列表
  static async getOrderList(params: any): Promise<any>
  
  // 创建订单
  static async createOrder(data: OrderFormData): Promise<CustomerOrderInfo>
  
  // 更新订单
  static async updateOrder(orderId: number, data: Partial<OrderFormData>): Promise<void>
}
```

### 4. 分拣管理模块

#### 功能特性
- **订单分配** - 管理员分配订单给分拣员
- **分拣大厅** - 未分配订单的公共分拣区域
- **分拣操作** - 分拣员执行分拣任务
- **进度跟踪** - 实时分拣进度

#### 核心接口
```typescript
export interface SortingTaskInfo {
  id: number
  order_no: string
  customer_name: string
  total_items: number
  sorted_items: number
  status: number
  assign_time: string
  sorting_progress: number
}

export class SortingService {
  // 获取分拣任务列表
  static async getSortingTaskList(params: any): Promise<any>

  // 分配订单
  static async assignOrder(orderId: number, sorterId: number): Promise<void>

  // 获取分拣大厅订单
  static async getHallOrders(params: any): Promise<any>

  // 领取分拣任务
  static async claimTask(orderId: number): Promise<void>

  // 更新分拣进度
  static async updateSortingProgress(orderId: number, items: SortingItemData[]): Promise<void>
}
```

### 5. 财务管理模块

#### 功能特性
- **收益统计** - 订单收益、成本分析
- **欠款管理** - 客户欠款跟踪
- **财务报表** - 各类财务报表生成
- **数据分析** - 经营数据分析

#### 核心接口
```typescript
export interface FinancialSummary {
  total_income: number
  total_cost: number
  net_profit: number
  pending_amount: number
  overdue_amount: number
}

export interface FinancialRecord {
  id: number
  record_type: number
  business_type: string
  amount: number
  description: string
  created_at: string
}

export class FinanceService {
  // 获取财务概览
  static async getFinancialSummary(params: any): Promise<FinancialSummary>

  // 获取财务记录
  static async getFinancialRecords(params: any): Promise<any>

  // 获取收益明细
  static async getIncomeDetails(params: any): Promise<any>

  // 获取欠款列表
  static async getDebtList(params: any): Promise<any>

  // 生成财务报表
  static async generateReport(type: string, params: any): Promise<any>
}
```

---

## 🔧 技术实现方案

### 1. 后端控制器设计

#### 会员管理控制器
```php
<?php
declare(strict_types=1);

namespace app\adminapi\controller;

use app\common\controller\BaseController;
use app\adminapi\validate\MemberValidate;
use app\common\service\MemberService;
use think\response\Json;

class Member extends BaseController
{
    /**
     * 会员列表
     */
    public function list(): Json
    {
        try {
            $params = $this->request->param();
            $result = MemberService::getList($params);
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 创建会员
     */
    public function create(): Json
    {
        try {
            $data = $this->request->post();

            $validate = new MemberValidate();
            if (!$validate->scene('create')->check($data)) {
                return $this->error($validate->getError());
            }

            $member = MemberService::create($data);
            return $this->success($member->toArray(), '创建成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 开通模块
     */
    public function enableModule(): Json
    {
        try {
            $data = $this->request->post();

            $validate = new MemberValidate();
            if (!$validate->scene('enable_module')->check($data)) {
                return $this->error($validate->getError());
            }

            MemberService::enableModule($data['member_id'], $data['module_code'], $data['expire_time']);
            return $this->success([], '开通成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 创建子账号
     */
    public function createSubAccount(): Json
    {
        try {
            $data = $this->request->post();
            $parentId = $this->request->param('parent_id');

            $validate = new MemberValidate();
            if (!$validate->scene('create_sub')->check($data)) {
                return $this->error($validate->getError());
            }

            $subAccount = MemberService::createSubAccount($parentId, $data);
            return $this->success($subAccount->toArray(), '创建成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 删除会员（级联删除所有相关数据）
     */
    public function delete(): Json
    {
        try {
            $id = $this->request->param('id');

            MemberService::deleteWithCascade($id);
            return $this->success([], '删除成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
```

#### 入库管理控制器
```php
<?php
declare(strict_types=1);

namespace app\adminapi\controller;

use app\common\controller\BaseController;
use app\adminapi\validate\PurchaseOrderValidate;
use app\common\service\StorageService;
use think\response\Json;

class Storage extends BaseController
{
    /**
     * 进货单列表
     */
    public function purchaseOrderList(): Json
    {
        try {
            $params = $this->request->param();
            $result = StorageService::getPurchaseOrderList($params);
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 创建进货单
     */
    public function createPurchaseOrder(): Json
    {
        try {
            $data = $this->request->post();

            $validate = new PurchaseOrderValidate();
            if (!$validate->scene('create')->check($data)) {
                return $this->error($validate->getError());
            }

            $order = StorageService::createPurchaseOrder($data);
            return $this->success($order->toArray(), '创建成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 入库操作
     */
    public function storageOperation(): Json
    {
        try {
            $data = $this->request->post();
            $orderId = $this->request->param('order_id');

            $validate = new PurchaseOrderValidate();
            if (!$validate->scene('storage')->check($data)) {
                return $this->error($validate->getError());
            }

            StorageService::storageOperation($orderId, $data['items']);
            return $this->success([], '入库成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
```

### 2. 前端页面设计

#### 会员管理页面配置
```typescript
// admin/src/views/system/member/index.vue
const memberListConfig: CrudListConfig<MemberInfo> = {
  api: {
    list: MemberService.getMemberList,
    create: MemberService.createMember,
    update: MemberService.updateMember,
    delete: MemberService.deleteMember
  },
  columns: [
    { type: 'selection' },
    { type: 'index', label: '序号', width: 60 },
    { prop: 'username', label: '用户名', minWidth: 120 },
    { prop: 'nickname', label: '昵称', minWidth: 120 },
    {
      prop: 'member_type',
      label: '会员类型',
      width: 100,
      formatter: (row: MemberInfo) => {
        const typeMap = { 1: '普通会员', 2: '管理员开通' }
        return typeMap[row.member_type] || '未知'
      }
    },
    { prop: 'expire_time', label: '到期时间', width: 160 },
    {
      prop: 'sub_accounts',
      label: '子账号',
      width: 100,
      formatter: (row: MemberInfo) => `${row.current_sub_accounts}/${row.max_sub_accounts}`
    },
    {
      prop: 'status',
      label: '状态',
      width: 80,
      formatter: (row: MemberInfo) => {
        const type = row.status ? 'success' : 'danger'
        const text = row.status ? '正常' : '禁用'
        return h(ElTag, { type, size: 'small' }, () => text)
      }
    },
    { prop: 'created_at', label: '创建时间', width: 160 }
  ],
  search: {
    items: [
      {
        prop: 'username',
        label: '用户名',
        type: 'input',
        placeholder: '请输入用户名'
      },
      {
        prop: 'member_type',
        label: '会员类型',
        type: 'select',
        options: [
          { label: '全部', value: '' },
          { label: '普通会员', value: 1 },
          { label: '管理员开通', value: 2 }
        ]
      },
      {
        prop: 'status',
        label: '状态',
        type: 'select',
        options: [
          { label: '全部', value: '' },
          { label: '正常', value: 1 },
          { label: '禁用', value: 0 }
        ]
      }
    ]
  },
  form: {
    items: [
      {
        prop: 'username',
        label: '用户名',
        type: 'input',
        required: true,
        placeholder: '请输入用户名'
      },
      {
        prop: 'password',
        label: '密码',
        type: 'password',
        required: true,
        placeholder: '请输入密码'
      },
      {
        prop: 'nickname',
        label: '昵称',
        type: 'input',
        required: true,
        placeholder: '请输入昵称'
      },
      {
        prop: 'phone',
        label: '手机号',
        type: 'input',
        placeholder: '请输入手机号'
      },
      {
        prop: 'expire_time',
        label: '到期时间',
        type: 'datetime',
        required: true
      },
      {
        prop: 'max_sub_accounts',
        label: '最大子账号数',
        type: 'number',
        defaultValue: 0,
        placeholder: '请输入最大子账号数量'
      },
      {
        prop: 'modules',
        label: '开通模块',
        type: 'checkbox',
        options: [
          { label: '入库功能', value: 'storage' },
          { label: '分拣功能', value: 'sorting' },
          { label: '录单功能', value: 'order_entry' },
          { label: '财务功能', value: 'finance' }
        ]
      }
    ]
  },
  actions: {
    create: {
      enabled: true,
      text: '新增会员'
    },
    edit: {
      enabled: true
    },
    delete: {
      enabled: true,
      confirmText: '确定要删除该会员吗？删除后将清除所有相关数据！'
    },
    custom: [
      {
        text: '模块管理',
        type: 'primary',
        handler: (row: MemberInfo) => {
          // 打开模块管理弹窗
          openModuleDialog(row)
        }
      },
      {
        text: '子账号',
        type: 'success',
        handler: (row: MemberInfo) => {
          // 跳转到子账号管理页面
          router.push(`/system/member/sub-accounts/${row.id}`)
        }
      }
    ]
  }
}
```

---

## 🚀 开发计划

### 第一阶段：基础架构搭建 (1-2周)
1. **数据库设计** - 创建所有核心表结构
2. **用户权限系统** - 实现多级会员体系
3. **基础CRUD** - 完成会员管理基础功能
4. **权限中间件** - 实现模块权限控制

### 第二阶段：核心业务开发 (2-3周)
1. **入库管理** - 进货单管理、入库操作
2. **录单功能** - 客户订单录入、管理
3. **分拣系统** - 订单分配、分拣大厅
4. **基础财务** - 收益统计、记录管理

### 第三阶段：高级功能 (1-2周)
1. **财务报表** - 各类报表生成
2. **数据分析** - 经营数据统计
3. **系统优化** - 性能优化、用户体验
4. **测试完善** - 功能测试、bug修复

---

## 📋 开发文件清单

### 后端文件结构
```
server/app/
├── adminapi/
│   ├── controller/
│   │   ├── Member.php              # 会员管理
│   │   ├── Storage.php             # 入库管理
│   │   ├── OrderEntry.php          # 录单管理
│   │   ├── Sorting.php             # 分拣管理
│   │   └── Finance.php             # 财务管理
│   ├── validate/
│   │   ├── MemberValidate.php      # 会员验证器
│   │   ├── PurchaseOrderValidate.php # 进货单验证器
│   │   ├── CustomerOrderValidate.php # 客户订单验证器
│   │   └── FinanceValidate.php     # 财务验证器
│   └── middleware/
│       └── ModuleAuth.php          # 模块权限中间件
├── common/
│   ├── model/
│   │   ├── Member.php              # 会员模型
│   │   ├── MemberModule.php        # 会员模块模型
│   │   ├── PurchaseOrder.php       # 进货单模型
│   │   ├── CustomerOrder.php       # 客户订单模型
│   │   └── FinancialRecord.php     # 财务记录模型
│   └── service/
│       ├── MemberService.php       # 会员服务
│       ├── StorageService.php      # 入库服务
│       ├── OrderEntryService.php   # 录单服务
│       ├── SortingService.php      # 分拣服务
│       └── FinanceService.php      # 财务服务
```

### 前端文件结构
```
admin/src/
├── api/
│   ├── memberApi.ts                # 会员API
│   ├── storageApi.ts               # 入库API
│   ├── orderEntryApi.ts            # 录单API
│   ├── sortingApi.ts               # 分拣API
│   └── financeApi.ts               # 财务API
├── views/
│   ├── member/
│   │   ├── index.vue               # 会员列表
│   │   ├── sub-accounts.vue        # 子账号管理
│   │   └── module-manage.vue       # 模块管理
│   ├── storage/
│   │   ├── purchase-order.vue      # 进货单管理
│   │   └── storage-operation.vue   # 入库操作
│   ├── order-entry/
│   │   ├── index.vue               # 订单录入
│   │   └── customer-manage.vue     # 客户管理
│   ├── sorting/
│   │   ├── task-list.vue           # 分拣任务
│   │   ├── hall.vue                # 分拣大厅
│   │   └── progress.vue            # 分拣进度
│   └── finance/
│       ├── overview.vue            # 财务概览
│       ├── records.vue             # 财务记录
│       ├── income.vue              # 收益明细
│       └── debt.vue                # 欠款管理
└── types/
    ├── member.ts                   # 会员类型定义
    ├── storage.ts                  # 入库类型定义
    ├── order.ts                    # 订单类型定义
    └── finance.ts                  # 财务类型定义
```

---

## 🔒 安全考虑

### 1. 数据安全
- **级联删除** - 删除会员时自动清理所有相关数据
- **权限隔离** - 不同会员只能访问自己的数据
- **操作日志** - 记录所有重要操作

### 2. 业务安全
- **模块权限** - 严格控制功能模块访问权限
- **到期检查** - 实时检查会员和模块到期状态
- **数据备份** - 重要数据定期备份

### 3. 系统安全
- **输入验证** - 所有输入数据严格验证
- **SQL注入防护** - 使用ORM防止SQL注入
- **XSS防护** - 前端输出内容转义

---

## 💡 技术亮点

### 1. 多级权限体系
- **灵活的会员层级** - 支持管理员开通会员，会员开通子账号
- **精细的模块控制** - 每个模块独立的权限和到期时间
- **动态权限检查** - 实时检查用户权限状态

### 2. 业务流程优化
- **智能分拣大厅** - 未分配订单自动进入公共分拣区域
- **实时状态跟踪** - 订单从录入到完成的全流程跟踪
- **灵活的分配机制** - 支持管理员分配和分拣员自主领取

### 3. 数据完整性
- **级联删除机制** - 确保删除会员时清理所有相关数据
- **事务处理** - 关键操作使用数据库事务保证一致性
- **数据关联** - 完善的外键关系保证数据完整性

这个开发方案基于您现有的优秀架构，充分利用了通用CRUD组件和自动路由系统，可以快速高效地开发出功能完善的AI分拣系统。
```
```
