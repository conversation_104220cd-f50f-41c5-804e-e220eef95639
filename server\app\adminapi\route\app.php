<?php
use think\facade\Route;

// 后台管理API路由组

// 认证相关
Route::group('auth', function () {
    Route::post('login', 'Auth/login');
    Route::post('logout', 'Auth/logout')->middleware(['adminapi_auth']);
    Route::get('info', 'Auth/info')->middleware(['adminapi_auth']);
    Route::post('refresh', 'Auth/refresh');
});

// 用户管理
Route::group('user', function () {
    Route::get('list', 'User/list');
    Route::post('create', 'User/create');
    Route::put('update/:id', 'User/update');
    Route::delete('delete/:id', 'User/delete');
})->middleware(['adminapi_auth']);

// 管理员管理
Route::group('admin', function () {
    Route::get('list', 'Admin/list');
    Route::post('create', 'Admin/create');
    Route::put('update/:id', 'Admin/update');
    Route::delete('delete/:id', 'Admin/delete');
    Route::get('roles', 'Admin/getRoles');
    Route::post('reset-password/:id', 'Admin/resetPassword');
    Route::post('toggle-status/:id', 'Admin/toggleStatus');
})->middleware(['adminapi_auth']);

// 角色管理
Route::group('role', function () {
    Route::get('list', 'Role/list');
    Route::post('create', 'Role/create');
    Route::put('update/:id', 'Role/update');
    Route::delete('delete/:id', 'Role/delete');
    Route::post('permissions/:id', 'Role/setPermissions');
})->middleware(['adminapi_auth']);

// 缓存管理
Route::group('cache', function () {
    Route::get('stats', 'Cache/stats');
    Route::get('check-connection', 'Cache/checkConnection');
    Route::post('clear', 'Cache/clear');
    Route::get('keys', 'Cache/keys');
    Route::get('performance-test', 'Cache/performanceTest');
})->middleware(['adminapi_auth']);



// 入库管理（需要storage模块权限）
Route::group('storage', function () {
    // 进货单管理
    Route::get('purchase-order/list', 'Storage/purchaseOrderList');
    Route::post('purchase-order/create', 'Storage/createPurchaseOrder');
    Route::put('purchase-order/update/:id', 'Storage/updatePurchaseOrder');
    Route::delete('purchase-order/delete/:id', 'Storage/deletePurchaseOrder');
    Route::get('purchase-order/detail/:id', 'Storage/purchaseOrderDetail');
    Route::get('purchase-order/generate-no', 'Storage/generateOrderNo');
    Route::get('purchase-order/pending-items', 'Storage/pendingStorageItems');

    // 入库操作
    Route::post('operation', 'Storage/storageOperation');
    Route::post('batch-operation', 'Storage/batchStorage');

    // 库存管理
    Route::get('inventory/list', 'Storage/inventoryList');
    Route::post('inventory/adjust', 'Storage/adjustInventory');
    Route::get('inventory/stats', 'Storage/inventoryStats');
})->middleware(['adminapi_auth', 'module_auth:storage']);

// 菜单管理
Route::group('menu', function () {
    Route::get('list', 'Menu/list');        // 后端控制模式专用
    Route::get('tree', 'Menu/tree');        // 菜单树形结构
    Route::post('create', 'Menu/create');
    Route::put('update/:id', 'Menu/update');
    Route::delete('delete/:id', 'Menu/delete');
})->middleware(['adminapi_auth']);

// 文件上传
Route::group('upload', function () {
    Route::post('image', 'Upload/image');
    Route::post('file', 'Upload/file');
})->middleware(['adminapi_auth']);

// 系统配置
Route::group('system', function () {
    Route::get('config', 'System/getConfig');
    Route::post('config', 'System/setConfig');
    Route::get('info', 'System/info');
})->middleware(['adminapi_auth']);

// 文章管理
Route::group('article', function () {
    Route::get('list', 'Article/list');
    Route::get('detail/:id', 'Article/detail');
    Route::post('create', 'Article/create');
    Route::put('update/:id', 'Article/update');
    Route::delete('delete/:id', 'Article/delete');
    Route::post('batch-delete', 'Article/batchDelete');
    Route::put('status/:id', 'Article/updateStatus');
    Route::put('publish/:id', 'Article/publish');
    Route::get('status-options', 'Article/statusOptions');
    Route::get('published-options', 'Article/publishedOptions');
})->middleware(['adminapi_auth']);

// 分类管理
Route::group('category', function () {
    Route::get('list', 'Category/list');
    Route::post('create', 'Category/create');
    Route::put('update/:id', 'Category/update');
    Route::delete('delete/:id', 'Category/delete');
})->middleware(['adminapi_auth']);

// 公告管理
Route::group('notice', function () {
    Route::get('list', 'Notice/list');
    Route::get('detail/:id', 'Notice/detail');
    Route::post('create', 'Notice/create');
    Route::put('update/:id', 'Notice/update');
    Route::delete('delete/:id', 'Notice/delete');
})->middleware(['adminapi_auth']);

// 媒体文件管理
Route::group('media', function () {
    Route::get('', 'Media/index');           // 获取媒体文件列表
    Route::post('upload', 'Media/upload');   // 上传媒体文件
    Route::delete(':id', 'Media/delete');    // 删除媒体文件
    Route::post('batch-delete', 'Media/batchDelete'); // 批量删除
})->middleware(['adminapi_auth']);
Route::group('media', function () {
    Route::get('', 'Media/index');
    Route::post('upload', 'Media/upload');
    Route::delete(':id', 'Media/delete');
    Route::post('batch-delete', 'Media/batchDelete');
    Route::post('move-to-category', 'Media/moveToCategory');
    Route::get('categories', 'Media/categories');
    Route::post('categories', 'Media/createCategory');
    Route::put('categories/:id', 'Media/updateCategory');
    Route::delete('categories/:id', 'Media/deleteCategory');
})->middleware(['adminapi_auth']);

// 测试路由
Route::get('test', 'Test/index');
Route::get('simple-test', 'SimpleTest/index');
