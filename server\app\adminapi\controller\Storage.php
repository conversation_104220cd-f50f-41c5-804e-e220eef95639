<?php
declare(strict_types=1);

namespace app\adminapi\controller;

use app\common\controller\BaseController;
use app\adminapi\validate\PurchaseOrderValidate;
use app\common\service\StorageService;
use app\common\model\PurchaseOrder;
use think\response\Json;

/**
 * 入库管理控制器
 */
class Storage extends BaseController
{
    /**
     * 进货单列表
     */
    public function purchaseOrderList(): Json
    {
        $params = $this->request->param();
        $result = StorageService::getPurchaseOrderList($params);
        return $this->success($result);
    }

    /**
     * 创建进货单
     */
    public function createPurchaseOrder(): Json
    {
        $data = $this->request->param();

        try {
            validate(PurchaseOrderValidate::class)->scene('create')->check($data);
            
            // 获取当前用户信息
            $userInfo = $this->getUserInfo();
            $data['member_id'] = $userInfo['id'];
            $data['created_by'] = $userInfo['id'];

            $result = StorageService::createPurchaseOrder($data);
            return $this->success($result, '创建成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 更新进货单
     */
    public function updatePurchaseOrder(): Json
    {
        $id = $this->request->param('id');
        $data = $this->request->param();

        try {
            validate(PurchaseOrderValidate::class)->scene('update')->check($data);
            
            $result = StorageService::updatePurchaseOrder((int)$id, $data);
            return $this->success($result, '更新成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 删除进货单
     */
    public function deletePurchaseOrder(): Json
    {
        $id = $this->request->param('id');

        try {
            $result = StorageService::deletePurchaseOrder((int)$id);
            return $this->success($result, '删除成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取进货单详情
     */
    public function purchaseOrderDetail(): Json
    {
        $id = $this->request->param('id');

        try {
            $result = StorageService::getPurchaseOrderDetail((int)$id);
            if (!$result) {
                return $this->error('进货单不存在');
            }
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 入库操作
     */
    public function storageOperation(): Json
    {
        $data = $this->request->param();

        try {
            validate(PurchaseOrderValidate::class)->scene('storage')->check($data);
            
            // 获取当前用户信息
            $userInfo = $this->getUserInfo();
            $data['operator_id'] = $userInfo['id'];

            $result = StorageService::performStorage($data);
            return $this->success($result, '入库成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 库存列表
     */
    public function inventoryList(): Json
    {
        $params = $this->request->param();
        
        // 获取当前用户信息
        $userInfo = $this->getUserInfo();
        $params['member_id'] = $userInfo['id'];

        $result = StorageService::getInventoryList($params);
        return $this->success($result);
    }

    /**
     * 库存调整
     */
    public function adjustInventory(): Json
    {
        $data = $this->request->param();

        try {
            // 获取当前用户信息
            $userInfo = $this->getUserInfo();
            $data['member_id'] = $userInfo['id'];
            $data['operator_id'] = $userInfo['id'];

            $result = StorageService::adjustInventory($data);
            return $this->success($result, '库存调整成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 库存统计
     */
    public function inventoryStats(): Json
    {
        try {
            // 获取当前用户信息
            $userInfo = $this->getUserInfo();

            $result = StorageService::getInventoryStats($userInfo['id']);
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 生成进货单号
     */
    public function generateOrderNo(): Json
    {
        try {
            $orderNo = StorageService::generatePurchaseOrderNo();
            return $this->success(['order_no' => $orderNo]);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取待入库商品列表
     */
    public function pendingStorageItems(): Json
    {
        try {
            // 获取当前用户信息
            $userInfo = $this->getUserInfo();

            $result = StorageService::getPendingStorageItems($userInfo['id']);
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 批量入库
     */
    public function batchStorage(): Json
    {
        $data = $this->request->param();

        try {
            // 获取当前用户信息
            $userInfo = $this->getUserInfo();
            $data['operator_id'] = $userInfo['id'];

            $result = StorageService::batchStorage($data);
            return $this->success($result, '批量入库成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取当前用户信息
     */
    private function getUserInfo(): array
    {
        // 这里应该从JWT或session中获取用户信息
        // 暂时返回模拟数据，实际开发中需要实现
        return [
            'id' => 1,
            'username' => 'admin',
            'nickname' => '管理员'
        ];
    }
}
