<?php
// 执行AI分拣系统扩展脚本
require_once 'vendor/autoload.php';

use think\facade\Db;

try {
    // 初始化ThinkPHP
    $app = new \think\App();
    $app->initialize();
    
    echo "开始执行AI分拣系统扩展...\n";
    echo "基于现有电商系统进行扩展，保留原有数据\n\n";
    
    // 读取SQL文件
    $sqlFile = '../ai_sorting_extension.sql';
    if (!file_exists($sqlFile)) {
        echo "❌ SQL文件不存在: {$sqlFile}\n";
        exit(1);
    }

    $sql = file_get_contents($sqlFile);
    if (!$sql) {
        echo "❌ 无法读取SQL文件\n";
        exit(1);
    }

    echo "✓ 成功读取扩展SQL文件\n";

    // 分割SQL语句
    $statements = splitSqlStatements($sql);
    echo "✓ 找到 " . count($statements) . " 条SQL语句\n\n";

    // 执行每条SQL语句
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $index => $statement) {
        $statement = trim($statement);
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }

        try {
            Db::execute($statement);
            $successCount++;
            
            // 显示执行的操作类型
            if (stripos($statement, 'DROP TABLE') !== false) {
                preg_match('/DROP TABLE.*?`([^`]+)`/i', $statement, $matches);
                $tableName = $matches[1] ?? 'unknown';
                echo "✓ 清理表: {$tableName}\n";
            } elseif (stripos($statement, 'CREATE TABLE') !== false) {
                preg_match('/CREATE TABLE.*?`([^`]+)`/i', $statement, $matches);
                $tableName = $matches[1] ?? 'unknown';
                echo "✓ 创建表: {$tableName}\n";
            } elseif (stripos($statement, 'ALTER TABLE') !== false) {
                preg_match('/ALTER TABLE.*?`([^`]+)`/i', $statement, $matches);
                $tableName = $matches[1] ?? 'unknown';
                echo "✓ 添加约束: {$tableName}\n";
            } elseif (stripos($statement, 'INSERT INTO') !== false) {
                preg_match('/INSERT INTO.*?`([^`]+)`/i', $statement, $matches);
                $tableName = $matches[1] ?? 'unknown';
                echo "✓ 插入数据: {$tableName}\n";
            } else {
                echo "✓ 执行成功: 语句 " . ($index + 1) . "\n";
            }
        } catch (\Exception $e) {
            $errorCount++;
            echo "✗ 执行失败: 语句 " . ($index + 1) . " - " . $e->getMessage() . "\n";
            // 继续执行其他语句
        }
    }

    echo "\n扩展完成！成功执行 {$successCount} 条语句";
    if ($errorCount > 0) {
        echo "，失败 {$errorCount} 条语句";
    }
    echo "\n\n";

    // 验证扩展结果
    verifyExtension();

} catch (\Exception $e) {
    echo "❌ 扩展失败: " . $e->getMessage() . "\n";
    exit(1);
}

/**
 * 分割SQL语句
 */
function splitSqlStatements(string $sql): array
{
    // 移除注释
    $sql = preg_replace('/--.*$/m', '', $sql);
    
    // 按分号分割，但要考虑字符串中的分号
    $statements = [];
    $current = '';
    $inString = false;
    $stringChar = '';
    
    for ($i = 0; $i < strlen($sql); $i++) {
        $char = $sql[$i];
        
        if (!$inString && ($char === '"' || $char === "'")) {
            $inString = true;
            $stringChar = $char;
        } elseif ($inString && $char === $stringChar) {
            $inString = false;
            $stringChar = '';
        } elseif (!$inString && $char === ';') {
            $statements[] = trim($current);
            $current = '';
            continue;
        }
        
        $current .= $char;
    }
    
    if (trim($current)) {
        $statements[] = trim($current);
    }
    
    return array_filter($statements);
}

/**
 * 验证扩展结果
 */
function verifyExtension(): void
{
    echo "验证AI分拣系统扩展结果:\n";

    $aiTables = [
        'fs_ai_members' => 'AI分拣会员表',
        'fs_ai_member_modules' => 'AI分拣会员模块权限表',
        'fs_ai_purchase_orders' => 'AI分拣进货单表',
        'fs_ai_purchase_order_items' => 'AI分拣进货单商品明细表',
        'fs_ai_customer_orders' => 'AI分拣客户订单表',
        'fs_ai_customer_order_items' => 'AI分拣客户订单商品明细表',
        'fs_ai_inventory' => 'AI分拣库存表',
        'fs_ai_financial_records' => 'AI分拣财务记录表'
    ];

    $allTablesExist = true;
    foreach ($aiTables as $tableName => $description) {
        try {
            $exists = Db::query("SHOW TABLES LIKE '{$tableName}'");
            if (!empty($exists)) {
                echo "✓ {$tableName} ({$description})\n";
            } else {
                echo "✗ {$tableName} ({$description}) - 不存在\n";
                $allTablesExist = false;
            }
        } catch (\Exception $e) {
            echo "✗ {$tableName} ({$description}) - 检查失败\n";
            $allTablesExist = false;
        }
    }

    if ($allTablesExist) {
        echo "\n检查AI分拣系统默认数据:\n";
        
        try {
            $aiAdmin = Db::table('fs_ai_members')->where('username', 'ai_admin')->find();
            if ($aiAdmin) {
                echo "✓ AI分拣管理员账号存在 (ID: {$aiAdmin['id']})\n";
                
                $modules = Db::table('fs_ai_member_modules')->where('member_id', $aiAdmin['id'])->select();
                echo "✓ AI分拣管理员模块权限: " . count($modules) . " 个模块\n";
                foreach ($modules as $module) {
                    echo "  - {$module['module_name']}: {$module['module_title']}\n";
                }
            } else {
                echo "✗ AI分拣管理员账号不存在\n";
            }
        } catch (\Exception $e) {
            echo "✗ 检查AI分拣系统默认数据失败: " . $e->getMessage() . "\n";
        }

        echo "\n验证原有系统完整性:\n";
        try {
            $adminCount = Db::table('fs_admin')->count();
            $userCount = Db::table('fs_user')->count();
            $roleCount = Db::table('fs_role')->count();
            echo "✓ 原有系统完整 - 管理员: {$adminCount}, 用户: {$userCount}, 角色: {$roleCount}\n";
        } catch (\Exception $e) {
            echo "✗ 原有系统检查失败\n";
        }

        echo "\n🎉 AI分拣系统扩展验证完成！\n";
        echo "AI分拣管理员账号: ai_admin / password\n";
        echo "原有电商系统保持完整，可继续使用\n";
    } else {
        echo "\n❌ 部分表创建失败，请检查错误信息\n";
    }
}
