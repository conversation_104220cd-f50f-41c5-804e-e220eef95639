<?php
declare(strict_types=1);

namespace app\common\model;

use think\Model;

/**
 * 客户订单模型
 */
class CustomerOrder extends Model
{
    // 表名
    protected $table = 'fs_customer_orders';

    // 主键
    protected $pk = 'id';

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // 字段类型转换
    protected $type = [
        'member_id' => 'integer',
        'total_amount' => 'float',
        'selling_price' => 'float',
        'cost_price' => 'float',
        'profit' => 'float',
        'status' => 'integer',
        'assigned_to' => 'integer',
        'assigned_at' => 'datetime',
        'completed_at' => 'datetime',
        'created_by' => 'integer',
    ];

    // 获取器 - 状态文本
    public function getStatusTextAttr($value, $data)
    {
        $status = [
            1 => '待定价',
            2 => '待分配',
            3 => '分拣中',
            4 => '已完成',
            5 => '已取消'
        ];
        return $status[$data['status']] ?? '未知';
    }

    // 关联会员
    public function member()
    {
        return $this->belongsTo(Member::class, 'member_id', 'id');
    }

    // 关联分拣员
    public function assignedMember()
    {
        return $this->belongsTo(Member::class, 'assigned_to', 'id');
    }

    // 关联录单员
    public function creator()
    {
        return $this->belongsTo(Member::class, 'created_by', 'id');
    }

    // 关联商品明细
    public function items()
    {
        return $this->hasMany(CustomerOrderItem::class, 'customer_order_id', 'id');
    }
}
