<?php
declare(strict_types=1);

namespace app\adminapi\controller;

use app\common\controller\BaseController;
use app\adminapi\validate\MemberValidate;
use app\common\service\MemberService;
use app\common\model\Member as MemberModel;
use app\common\model\MemberModule;
use think\response\Json;

/**
 * 会员管理控制器
 */
class Member extends BaseController
{
    /**
     * 会员列表
     */
    public function list(): Json
    {
        $params = $this->request->param();
        $result = MemberService::getList($params);
        return $this->success($result);
    }

    /**
     * 创建会员
     */
    public function create(): Json
    {
        $data = $this->request->param();

        try {
            validate(MemberValidate::class)->scene('create')->check($data);

            // 检查用户名是否存在
            if (MemberService::checkUsername($data['username'])) {
                return $this->error('用户名已存在');
            }

            // 检查上级会员是否可以创建子账号
            if (!empty($data['parent_id'])) {
                $parent = MemberModel::find($data['parent_id']);
                if (!$parent || !$parent->canCreateSubAccount()) {
                    return $this->error('上级会员子账号数量已达上限');
                }
            }

            $result = MemberService::create($data);
            return $this->success($result, '创建成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 更新会员
     */
    public function update(): Json
    {
        $id = $this->request->param('id');
        $data = $this->request->param();

        try {
            validate(MemberValidate::class)->scene('update')->check($data);

            // 检查用户名是否存在（排除自己）
            if (!empty($data['username']) && MemberService::checkUsername($data['username'], (int)$id)) {
                return $this->error('用户名已存在');
            }

            $result = MemberService::update((int)$id, $data);
            return $this->success($result, '更新成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 删除会员
     */
    public function delete(): Json
    {
        $id = $this->request->param('id');

        try {
            $result = MemberService::delete((int)$id);
            return $this->success($result, '删除成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 批量删除会员
     */
    public function batchDelete(): Json
    {
        $ids = $this->request->param('ids', []);

        if (empty($ids) || !is_array($ids)) {
            return $this->error('请选择要删除的会员');
        }

        try {
            $successCount = 0;
            $errors = [];

            foreach ($ids as $id) {
                try {
                    MemberService::delete((int)$id);
                    $successCount++;
                } catch (\Exception $e) {
                    $errors[] = "ID {$id}: " . $e->getMessage();
                }
            }

            if ($successCount > 0) {
                $message = "成功删除 {$successCount} 个会员";
                if (!empty($errors)) {
                    $message .= "，失败：" . implode('; ', $errors);
                }
                return $this->success(['success_count' => $successCount, 'errors' => $errors], $message);
            } else {
                return $this->error('删除失败：' . implode('; ', $errors));
            }
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取会员详情
     */
    public function detail(): Json
    {
        $id = $this->request->param('id');

        try {
            $result = MemberService::getDetail((int)$id);
            if (!$result) {
                return $this->error('会员不存在');
            }
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取上级会员选项
     */
    public function parentOptions(): Json
    {
        try {
            $excludeId = $this->request->param('exclude_id', 0);
            $options = MemberService::getParentOptions((int)$excludeId);
            return $this->success($options);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取可用模块列表
     */
    public function moduleOptions(): Json
    {
        try {
            $modules = MemberModule::getAvailableModules();
            $options = [];
            foreach ($modules as $name => $title) {
                $options[] = [
                    'value' => $name,
                    'label' => $title
                ];
            }
            return $this->success($options);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 设置会员模块权限
     */
    public function setModules(): Json
    {
        $memberId = $this->request->param('member_id');
        $modules = $this->request->param('modules', []);

        try {
            if (empty($memberId)) {
                return $this->error('请指定会员ID');
            }

            $member = MemberModel::find($memberId);
            if (!$member) {
                return $this->error('会员不存在');
            }

            $result = MemberModule::setMemberModules((int)$memberId, $modules);
            
            if ($result) {
                // 清除缓存
                \app\common\service\CacheService::deleteMemberRelatedCache((int)$memberId);
                return $this->success([], '设置成功');
            } else {
                return $this->error('设置失败');
            }
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取会员模块权限
     */
    public function getModules(): Json
    {
        $memberId = $this->request->param('member_id');

        try {
            if (empty($memberId)) {
                return $this->error('请指定会员ID');
            }

            $modules = MemberModule::getMemberModules((int)$memberId);
            return $this->success($modules);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 重置会员密码
     */
    public function resetPassword(): Json
    {
        $id = $this->request->param('id');
        $newPassword = $this->request->param('password');

        try {
            if (empty($newPassword)) {
                return $this->error('请输入新密码');
            }

            if (strlen($newPassword) < 6) {
                return $this->error('密码长度不能少于6位');
            }

            $member = MemberModel::find($id);
            if (!$member) {
                return $this->error('会员不存在');
            }

            $member->password = $newPassword;
            $result = $member->save();

            if ($result) {
                return $this->success([], '密码重置成功');
            } else {
                return $this->error('密码重置失败');
            }
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取会员层级结构
     */
    public function hierarchy(): Json
    {
        $memberId = $this->request->param('member_id');

        try {
            if (empty($memberId)) {
                return $this->error('请指定会员ID');
            }

            $hierarchy = MemberService::getMemberHierarchy((int)$memberId);
            return $this->success($hierarchy);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 启用/禁用会员
     */
    public function toggleStatus(): Json
    {
        $id = $this->request->param('id');

        try {
            $member = MemberModel::find($id);
            if (!$member) {
                return $this->error('会员不存在');
            }

            $member->status = $member->status ? 0 : 1;
            $result = $member->save();

            if ($result) {
                // 清除缓存
                \app\common\service\CacheService::deleteMemberRelatedCache((int)$id);
                $statusText = $member->status ? '启用' : '禁用';
                return $this->success([], $statusText . '成功');
            } else {
                return $this->error('操作失败');
            }
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
