<?php
declare(strict_types=1);

namespace app\common\service;

use app\common\model\CustomerOrder;
use app\common\model\CustomerOrderItem;
use app\common\model\Member;
use app\common\model\Inventory;
use app\common\model\FinancialRecord;

/**
 * 录单管理服务类
 */
class OrderEntryService
{
    /**
     * 获取客户订单列表
     */
    public static function getCustomerOrderList(array $params = []): array
    {
        $page = $params['current'] ?? 1;
        $limit = $params['size'] ?? 20;

        $query = CustomerOrder::with(['member', 'assignedMember', 'creator'])
            ->order('id desc');

        // 搜索条件
        if (!empty($params['order_no'])) {
            $query->whereLike('order_no', '%' . $params['order_no'] . '%');
        }

        if (!empty($params['customer_name'])) {
            $query->whereLike('customer_name', '%' . $params['customer_name'] . '%');
        }

        if (!empty($params['customer_phone'])) {
            $query->whereLike('customer_phone', '%' . $params['customer_phone'] . '%');
        }

        if (isset($params['status']) && $params['status'] !== '') {
            $query->where('status', $params['status']);
        }

        if (!empty($params['member_id'])) {
            $query->where('member_id', $params['member_id']);
        }

        if (!empty($params['assigned_to'])) {
            $query->where('assigned_to', $params['assigned_to']);
        }

        if (!empty($params['start_date'])) {
            $query->whereTime('created_at', '>=', $params['start_date']);
        }

        if (!empty($params['end_date'])) {
            $query->whereTime('created_at', '<=', $params['end_date']);
        }

        $result = $query->paginate([
            'list_rows' => $limit,
            'page' => $page,
        ]);

        return [
            'list' => $result->items(),
            'total' => $result->total(),
            'current' => $page,
            'size' => $limit,
        ];
    }

    /**
     * 创建客户订单
     */
    public static function createCustomerOrder(array $data): CustomerOrder
    {
        try {
            // 开启事务
            CustomerOrder::startTrans();

            // 生成订单号
            if (empty($data['order_no'])) {
                $data['order_no'] = self::generateCustomerOrderNo();
            }

            // 创建客户订单
            $customerOrder = CustomerOrder::create([
                'order_no' => $data['order_no'],
                'member_id' => $data['member_id'],
                'customer_name' => $data['customer_name'],
                'customer_phone' => $data['customer_phone'],
                'customer_address' => $data['customer_address'],
                'total_amount' => $data['total_amount'] ?? 0,
                'status' => 1, // 待定价
                'remark' => $data['remark'] ?? '',
                'created_by' => $data['created_by'],
            ]);

            // 创建商品明细
            if (!empty($data['items'])) {
                foreach ($data['items'] as $item) {
                    CustomerOrderItem::create([
                        'customer_order_id' => $customerOrder->id,
                        'product_name' => $item['product_name'],
                        'product_sku' => $item['product_sku'] ?? '',
                        'product_spec' => $item['product_spec'] ?? '',
                        'quantity' => $item['quantity'],
                        'unit_price' => $item['unit_price'] ?? 0,
                        'amount' => ($item['unit_price'] ?? 0) * $item['quantity'],
                    ]);
                }

                // 更新订单总金额
                $totalAmount = array_sum(array_map(function($item) {
                    return ($item['unit_price'] ?? 0) * $item['quantity'];
                }, $data['items']));

                $customerOrder->total_amount = $totalAmount;
                $customerOrder->save();
            }

            // 提交事务
            CustomerOrder::commit();

            return $customerOrder;
        } catch (\Exception $e) {
            // 回滚事务
            CustomerOrder::rollback();
            throw $e;
        }
    }

    /**
     * 更新客户订单
     */
    public static function updateCustomerOrder(int $id, array $data): bool
    {
        try {
            // 开启事务
            CustomerOrder::startTrans();

            $customerOrder = CustomerOrder::find($id);
            if (!$customerOrder) {
                throw new \Exception('订单不存在');
            }

            // 只有待定价状态才能修改
            if ($customerOrder->status != 1) {
                throw new \Exception('只有待定价状态的订单才能修改');
            }

            // 更新基本信息
            $updateData = [];
            $allowFields = ['customer_name', 'customer_phone', 'customer_address', 'remark'];
            
            foreach ($allowFields as $field) {
                if (isset($data[$field])) {
                    $updateData[$field] = $data[$field];
                }
            }

            $customerOrder->save($updateData);

            // 更新商品明细
            if (isset($data['items'])) {
                // 删除原有明细
                CustomerOrderItem::where('customer_order_id', $id)->delete();

                // 添加新明细
                foreach ($data['items'] as $item) {
                    CustomerOrderItem::create([
                        'customer_order_id' => $id,
                        'product_name' => $item['product_name'],
                        'product_sku' => $item['product_sku'] ?? '',
                        'product_spec' => $item['product_spec'] ?? '',
                        'quantity' => $item['quantity'],
                        'unit_price' => $item['unit_price'] ?? 0,
                        'amount' => ($item['unit_price'] ?? 0) * $item['quantity'],
                    ]);
                }

                // 重新计算总金额
                $totalAmount = array_sum(array_map(function($item) {
                    return ($item['unit_price'] ?? 0) * $item['quantity'];
                }, $data['items']));

                $customerOrder->total_amount = $totalAmount;
                $customerOrder->save();
            }

            // 提交事务
            CustomerOrder::commit();

            return true;
        } catch (\Exception $e) {
            // 回滚事务
            CustomerOrder::rollback();
            throw $e;
        }
    }

    /**
     * 删除客户订单
     */
    public static function deleteCustomerOrder(int $id): bool
    {
        try {
            // 开启事务
            CustomerOrder::startTrans();

            $customerOrder = CustomerOrder::find($id);
            if (!$customerOrder) {
                throw new \Exception('订单不存在');
            }

            // 只有待定价状态才能删除
            if ($customerOrder->status != 1) {
                throw new \Exception('只有待定价状态的订单才能删除');
            }

            // 删除商品明细
            CustomerOrderItem::where('customer_order_id', $id)->delete();

            // 删除订单
            $customerOrder->delete();

            // 提交事务
            CustomerOrder::commit();

            return true;
        } catch (\Exception $e) {
            // 回滚事务
            CustomerOrder::rollback();
            throw $e;
        }
    }

    /**
     * 获取客户订单详情
     */
    public static function getCustomerOrderDetail(int $id): ?array
    {
        $customerOrder = CustomerOrder::with(['member', 'assignedMember', 'creator', 'items'])->find($id);
        if (!$customerOrder) {
            return null;
        }

        return $customerOrder->toArray();
    }

    /**
     * 管理员定价
     */
    public static function setPricing(array $data): bool
    {
        try {
            // 开启事务
            CustomerOrder::startTrans();

            $orderId = $data['order_id'];
            $sellingPrice = $data['selling_price'];
            $costPrice = $data['cost_price'] ?? 0;

            $customerOrder = CustomerOrder::find($orderId);
            if (!$customerOrder) {
                throw new \Exception('订单不存在');
            }

            if ($customerOrder->status != 1) {
                throw new \Exception('只有待定价状态的订单才能定价');
            }

            // 更新定价信息
            $customerOrder->selling_price = $sellingPrice;
            $customerOrder->cost_price = $costPrice;
            $customerOrder->profit = $sellingPrice - $costPrice;
            $customerOrder->status = 2; // 待分配
            $customerOrder->save();

            // 提交事务
            CustomerOrder::commit();

            return true;
        } catch (\Exception $e) {
            // 回滚事务
            CustomerOrder::rollback();
            throw $e;
        }
    }

    /**
     * 分配分拣员
     */
    public static function assignSorter(array $data): bool
    {
        try {
            // 开启事务
            CustomerOrder::startTrans();

            $orderId = $data['order_id'];
            $sorterId = $data['sorter_id'];

            $customerOrder = CustomerOrder::find($orderId);
            if (!$customerOrder) {
                throw new \Exception('订单不存在');
            }

            if ($customerOrder->status != 2) {
                throw new \Exception('只有待分配状态的订单才能分配');
            }

            // 检查分拣员是否存在且有分拣权限
            $sorter = Member::find($sorterId);
            if (!$sorter || !$sorter->hasModulePermission('sorting')) {
                throw new \Exception('分拣员不存在或没有分拣权限');
            }

            // 分配分拣员
            $customerOrder->assigned_to = $sorterId;
            $customerOrder->assigned_at = date('Y-m-d H:i:s');
            $customerOrder->status = 3; // 分拣中
            $customerOrder->save();

            // 提交事务
            CustomerOrder::commit();

            return true;
        } catch (\Exception $e) {
            // 回滚事务
            CustomerOrder::rollback();
            throw $e;
        }
    }

    /**
     * 获取可用分拣员列表
     */
    public static function getSorterOptions(): array
    {
        $sorters = Member::where('status', 1)
            ->whereExists(function($query) {
                $query->table('fs_member_modules')
                    ->where('member_id', 'exp', 'fs_members.id')
                    ->where('module_name', 'sorting')
                    ->where('is_enabled', 1)
                    ->where(function($q) {
                        $q->whereNull('expire_time')
                          ->whereOr('expire_time', '>', date('Y-m-d H:i:s'));
                    });
            })
            ->field('id,username,nickname')
            ->select();

        $options = [];
        foreach ($sorters as $sorter) {
            $options[] = [
                'value' => $sorter->id,
                'label' => "{$sorter->username}({$sorter->nickname})"
            ];
        }

        return $options;
    }

    /**
     * 生成客户订单号
     */
    public static function generateCustomerOrderNo(): string
    {
        return 'CO' . date('Ymd') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
    }

    /**
     * 获取待定价订单列表
     */
    public static function getPendingPricingOrders(int $memberId): array
    {
        $orders = CustomerOrder::where('member_id', $memberId)
            ->where('status', 1) // 待定价
            ->with(['items'])
            ->order('created_at asc')
            ->select();

        return $orders->toArray();
    }

    /**
     * 获取待分配订单列表
     */
    public static function getPendingAssignOrders(int $memberId): array
    {
        $orders = CustomerOrder::where('member_id', $memberId)
            ->where('status', 2) // 待分配
            ->with(['items'])
            ->order('created_at asc')
            ->select();

        return $orders->toArray();
    }

    /**
     * 批量定价
     */
    public static function batchPricing(array $data): bool
    {
        try {
            // 开启事务
            CustomerOrder::startTrans();

            $orders = $data['orders'];

            foreach ($orders as $orderData) {
                $pricingData = [
                    'order_id' => $orderData['order_id'],
                    'selling_price' => $orderData['selling_price'],
                    'cost_price' => $orderData['cost_price'] ?? 0,
                    'operator_id' => $data['operator_id']
                ];

                self::setPricing($pricingData);
            }

            // 提交事务
            CustomerOrder::commit();

            return true;
        } catch (\Exception $e) {
            // 回滚事务
            CustomerOrder::rollback();
            throw $e;
        }
    }

    /**
     * 批量分配
     */
    public static function batchAssign(array $data): bool
    {
        try {
            // 开启事务
            CustomerOrder::startTrans();

            $orderIds = $data['order_ids'];
            $sorterId = $data['sorter_id'];

            foreach ($orderIds as $orderId) {
                $assignData = [
                    'order_id' => $orderId,
                    'sorter_id' => $sorterId,
                    'operator_id' => $data['operator_id']
                ];

                self::assignSorter($assignData);
            }

            // 提交事务
            CustomerOrder::commit();

            return true;
        } catch (\Exception $e) {
            // 回滚事务
            CustomerOrder::rollback();
            throw $e;
        }
    }

    /**
     * 取消订单
     */
    public static function cancelOrder(int $id, string $reason = ''): bool
    {
        try {
            $customerOrder = CustomerOrder::find($id);
            if (!$customerOrder) {
                throw new \Exception('订单不存在');
            }

            // 只有未完成的订单才能取消
            if ($customerOrder->status >= 4) {
                throw new \Exception('已完成或已取消的订单不能再次取消');
            }

            $customerOrder->status = 5; // 已取消
            $customerOrder->remark = ($customerOrder->remark ? $customerOrder->remark . "\n" : '') . "取消原因：{$reason}";
            
            return $customerOrder->save();
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * 获取订单统计
     */
    public static function getOrderStats(int $memberId): array
    {
        $totalOrders = CustomerOrder::where('member_id', $memberId)->count();
        $pendingPricing = CustomerOrder::where('member_id', $memberId)->where('status', 1)->count();
        $pendingAssign = CustomerOrder::where('member_id', $memberId)->where('status', 2)->count();
        $inProgress = CustomerOrder::where('member_id', $memberId)->where('status', 3)->count();
        $completed = CustomerOrder::where('member_id', $memberId)->where('status', 4)->count();
        $cancelled = CustomerOrder::where('member_id', $memberId)->where('status', 5)->count();

        $totalRevenue = CustomerOrder::where('member_id', $memberId)
            ->where('status', 4)
            ->sum('selling_price');

        $totalProfit = CustomerOrder::where('member_id', $memberId)
            ->where('status', 4)
            ->sum('profit');

        return [
            'total_orders' => $totalOrders,
            'pending_pricing' => $pendingPricing,
            'pending_assign' => $pendingAssign,
            'in_progress' => $inProgress,
            'completed' => $completed,
            'cancelled' => $cancelled,
            'total_revenue' => round($totalRevenue, 2),
            'total_profit' => round($totalProfit, 2),
        ];
    }
}
