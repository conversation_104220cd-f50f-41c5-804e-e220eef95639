<?php
declare(strict_types=1);

namespace app\common\model;

use think\Model;

/**
 * 客户订单商品明细模型
 */
class CustomerOrderItem extends Model
{
    // 表名
    protected $table = 'fs_customer_order_items';

    // 主键
    protected $pk = 'id';

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // 字段类型转换
    protected $type = [
        'customer_order_id' => 'integer',
        'quantity' => 'integer',
        'unit_price' => 'float',
        'amount' => 'float',
    ];

    // 关联客户订单
    public function customerOrder()
    {
        return $this->belongsTo(CustomerOrder::class, 'customer_order_id', 'id');
    }
}
