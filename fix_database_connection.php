<?php
// 修正数据库连接并迁移到aifen数据库
require_once 'vendor/autoload.php';

use think\facade\Db;
use think\facade\Config;

try {
    // 初始化ThinkPHP
    $app = new \think\App();
    $app->initialize();
    
    echo "检查数据库配置...\n";
    
    // 获取数据库配置
    $dbConfig = Config::get('database.connections.mysql');
    echo "当前数据库配置:\n";
    echo "- 主机: " . $dbConfig['hostname'] . "\n";
    echo "- 数据库: " . $dbConfig['database'] . "\n";
    echo "- 用户名: " . $dbConfig['username'] . "\n";
    echo "- 前缀: " . $dbConfig['prefix'] . "\n";
    
    // 检查当前连接的数据库
    $currentDb = Db::query("SELECT DATABASE() as db_name")[0]['db_name'];
    echo "\n当前实际连接的数据库: {$currentDb}\n";
    
    if ($currentDb !== 'aifen') {
        echo "\n需要切换到aifen数据库...\n";
        
        // 直接连接aifen数据库
        Db::execute("USE aifen");
        
        // 验证切换
        $newDb = Db::query("SELECT DATABASE() as db_name")[0]['db_name'];
        echo "切换后的数据库: {$newDb}\n";
        
        if ($newDb === 'aifen') {
            echo "✓ 成功切换到aifen数据库\n";
            
            // 检查aifen数据库中的表
            echo "\naifen数据库中的表:\n";
            $tables = Db::query("SHOW TABLES");
            foreach ($tables as $table) {
                $tableName = array_values($table)[0];
                echo "- {$tableName}\n";
            }
            
            // 检查是否需要导入fanshop数据
            $tableCount = count($tables);
            if ($tableCount < 10) {
                echo "\n⚠️  aifen数据库表较少({$tableCount}个)，可能需要从fanshop导入基础数据\n";
                echo "建议执行: mysqldump fanshop | mysql aifen\n";
            } else {
                echo "\n✓ aifen数据库已有{$tableCount}个表，数据完整\n";
            }
            
        } else {
            echo "❌ 切换数据库失败\n";
        }
    } else {
        echo "\n✓ 已经连接到aifen数据库\n";
        
        // 检查表数量
        $tables = Db::query("SHOW TABLES");
        echo "aifen数据库中有 " . count($tables) . " 个表\n";
    }
    
} catch (\Exception $e) {
    echo "❌ 操作失败: " . $e->getMessage() . "\n";
}
