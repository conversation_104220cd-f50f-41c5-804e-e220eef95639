<?php
// 数据库迁移脚本
require_once 'vendor/autoload.php';

use think\facade\Db;

try {
    // 初始化ThinkPHP
    $app = new \think\App();
    $app->initialize();
    
    echo "开始执行AI分拣系统数据库迁移...\n";
    
    // 读取SQL文件
    $sqlFile = 'database/migrations/create_ai_sorting_tables.sql';
    if (!file_exists($sqlFile)) {
        echo "❌ SQL文件不存在: {$sqlFile}\n";
        exit(1);
    }

    $sql = file_get_contents($sqlFile);
    if (!$sql) {
        echo "❌ 无法读取SQL文件\n";
        exit(1);
    }

    echo "✓ 成功读取SQL文件\n";

    // 分割SQL语句
    $statements = splitSqlStatements($sql);
    echo "✓ 找到 " . count($statements) . " 条SQL语句\n\n";

    // 执行每条SQL语句
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $index => $statement) {
        $statement = trim($statement);
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }

        try {
            Db::execute($statement);
            $successCount++;
            echo "✓ 执行成功: 语句 " . ($index + 1) . "\n";
        } catch (\Exception $e) {
            $errorCount++;
            echo "✗ 执行失败: 语句 " . ($index + 1) . " - " . $e->getMessage() . "\n";
            // 继续执行其他语句
        }
    }

    echo "\n迁移完成！成功执行 {$successCount} 条语句";
    if ($errorCount > 0) {
        echo "，失败 {$errorCount} 条语句";
    }
    echo "\n\n";

    // 验证表是否创建成功
    verifyTables();

} catch (\Exception $e) {
    echo "❌ 迁移失败: " . $e->getMessage() . "\n";
    exit(1);
}

/**
 * 分割SQL语句
 */
function splitSqlStatements(string $sql): array
{
    // 移除注释
    $sql = preg_replace('/--.*$/m', '', $sql);
    
    // 按分号分割，但要考虑字符串中的分号
    $statements = [];
    $current = '';
    $inString = false;
    $stringChar = '';
    
    for ($i = 0; $i < strlen($sql); $i++) {
        $char = $sql[$i];
        
        if (!$inString && ($char === '"' || $char === "'")) {
            $inString = true;
            $stringChar = $char;
        } elseif ($inString && $char === $stringChar) {
            $inString = false;
            $stringChar = '';
        } elseif (!$inString && $char === ';') {
            $statements[] = trim($current);
            $current = '';
            continue;
        }
        
        $current .= $char;
    }
    
    if (trim($current)) {
        $statements[] = trim($current);
    }
    
    return array_filter($statements);
}

/**
 * 验证表是否创建成功
 */
function verifyTables(): void
{
    echo "验证表结构:\n";

    $tables = [
        'fs_members' => '会员表',
        'fs_member_modules' => '会员模块权限表',
        'fs_purchase_orders' => '进货单表',
        'fs_purchase_order_items' => '进货单商品明细表',
        'fs_customer_orders' => '客户订单表',
        'fs_customer_order_items' => '客户订单商品明细表',
        'fs_inventory' => '库存表',
        'fs_financial_records' => '财务记录表'
    ];

    $allTablesExist = true;
    foreach ($tables as $tableName => $description) {
        try {
            $exists = Db::query("SHOW TABLES LIKE '{$tableName}'");
            if (!empty($exists)) {
                echo "✓ {$tableName} ({$description})\n";
            } else {
                echo "✗ {$tableName} ({$description}) - 不存在\n";
                $allTablesExist = false;
            }
        } catch (\Exception $e) {
            echo "✗ {$tableName} ({$description}) - 检查失败\n";
            $allTablesExist = false;
        }
    }

    if ($allTablesExist) {
        echo "\n检查默认数据:\n";
        
        try {
            $admin = Db::table('fs_members')->where('username', 'admin')->find();
            if ($admin) {
                echo "✓ 默认管理员账号存在 (ID: {$admin['id']})\n";
                
                $modules = Db::table('fs_member_modules')->where('member_id', $admin['id'])->select();
                echo "✓ 管理员模块权限: " . count($modules) . " 个模块\n";
                foreach ($modules as $module) {
                    echo "  - {$module['module_name']}: {$module['module_title']}\n";
                }
            } else {
                echo "✗ 默认管理员账号不存在\n";
            }
        } catch (\Exception $e) {
            echo "✗ 检查默认数据失败: " . $e->getMessage() . "\n";
        }

        echo "\n🎉 数据库迁移验证完成！\n";
        echo "默认管理员账号: admin / password\n";
    } else {
        echo "\n❌ 部分表创建失败，请检查错误信息\n";
    }
}
