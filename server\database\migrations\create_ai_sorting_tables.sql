-- AI分拣系统数据库表结构
-- 创建时间: 2025-01-31
-- 说明: 根据开发方案创建8个核心数据表

-- 1. 会员表 (fs_members)
CREATE TABLE `fs_members` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '会员ID',
  `parent_id` int(11) unsigned DEFAULT 0 COMMENT '上级会员ID，0表示管理员直接开通',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `nickname` varchar(100) DEFAULT '' COMMENT '昵称',
  `phone` varchar(20) DEFAULT '' COMMENT '手机号',
  `email` varchar(100) DEFAULT '' COMMENT '邮箱',
  `avatar` varchar(255) DEFAULT '' COMMENT '头像',
  `level` tinyint(1) DEFAULT 1 COMMENT '会员等级：1=普通会员，2=高级会员',
  `max_sub_accounts` int(11) DEFAULT 0 COMMENT '最大子账号数量',
  `current_sub_accounts` int(11) DEFAULT 0 COMMENT '当前子账号数量',
  `expire_time` datetime DEFAULT NULL COMMENT '会员到期时间',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0=禁用，1=启用',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT '' COMMENT '最后登录IP',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  KEY `parent_id` (`parent_id`),
  KEY `status` (`status`),
  KEY `expire_time` (`expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员表';

-- 2. 会员模块权限表 (fs_member_modules)
CREATE TABLE `fs_member_modules` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `member_id` int(11) unsigned NOT NULL COMMENT '会员ID',
  `module_name` varchar(50) NOT NULL COMMENT '模块名称：storage=入库，sorting=分拣，order_entry=录单，finance=财务',
  `module_title` varchar(100) NOT NULL COMMENT '模块标题',
  `is_enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用：0=禁用，1=启用',
  `expire_time` datetime DEFAULT NULL COMMENT '模块到期时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `member_module` (`member_id`, `module_name`),
  KEY `member_id` (`member_id`),
  KEY `module_name` (`module_name`),
  KEY `expire_time` (`expire_time`),
  CONSTRAINT `fk_member_modules_member` FOREIGN KEY (`member_id`) REFERENCES `fs_members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员模块权限表';

-- 3. 进货单表 (fs_purchase_orders)
CREATE TABLE `fs_purchase_orders` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '进货单ID',
  `member_id` int(11) unsigned NOT NULL COMMENT '会员ID',
  `order_no` varchar(50) NOT NULL COMMENT '进货单号',
  `supplier_name` varchar(100) NOT NULL COMMENT '供应商名称',
  `supplier_contact` varchar(100) DEFAULT '' COMMENT '供应商联系方式',
  `total_amount` decimal(10,2) DEFAULT 0.00 COMMENT '总金额',
  `total_quantity` int(11) DEFAULT 0 COMMENT '总数量',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1=待入库，2=部分入库，3=已完成',
  `remark` text COMMENT '备注',
  `created_by` int(11) unsigned NOT NULL COMMENT '创建人ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `member_id` (`member_id`),
  KEY `status` (`status`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `fk_purchase_orders_member` FOREIGN KEY (`member_id`) REFERENCES `fs_members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='进货单表';

-- 4. 进货单商品明细表 (fs_purchase_order_items)
CREATE TABLE `fs_purchase_order_items` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `purchase_order_id` int(11) unsigned NOT NULL COMMENT '进货单ID',
  `product_name` varchar(200) NOT NULL COMMENT '商品名称',
  `product_sku` varchar(100) DEFAULT '' COMMENT '商品SKU',
  `product_spec` varchar(200) DEFAULT '' COMMENT '商品规格',
  `purchase_price` decimal(8,2) DEFAULT 0.00 COMMENT '进货价',
  `quantity` int(11) NOT NULL COMMENT '数量',
  `stored_quantity` int(11) DEFAULT 0 COMMENT '已入库数量',
  `amount` decimal(10,2) DEFAULT 0.00 COMMENT '小计金额',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `purchase_order_id` (`purchase_order_id`),
  KEY `product_sku` (`product_sku`),
  CONSTRAINT `fk_purchase_items_order` FOREIGN KEY (`purchase_order_id`) REFERENCES `fs_purchase_orders` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='进货单商品明细表';

-- 5. 客户订单表 (fs_customer_orders)
CREATE TABLE `fs_customer_orders` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `member_id` int(11) unsigned NOT NULL COMMENT '会员ID',
  `order_no` varchar(50) NOT NULL COMMENT '订单号',
  `customer_name` varchar(100) NOT NULL COMMENT '客户姓名',
  `customer_phone` varchar(20) NOT NULL COMMENT '客户电话',
  `customer_address` text NOT NULL COMMENT '客户地址',
  `total_amount` decimal(10,2) DEFAULT 0.00 COMMENT '订单总金额',
  `selling_price` decimal(10,2) DEFAULT 0.00 COMMENT '销售价格（管理员定价）',
  `cost_price` decimal(10,2) DEFAULT 0.00 COMMENT '成本价格',
  `profit` decimal(10,2) DEFAULT 0.00 COMMENT '利润',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1=待定价，2=待分配，3=分拣中，4=已完成，5=已取消',
  `assigned_to` int(11) unsigned DEFAULT NULL COMMENT '分配给的分拣员ID',
  `assigned_at` datetime DEFAULT NULL COMMENT '分配时间',
  `completed_at` datetime DEFAULT NULL COMMENT '完成时间',
  `remark` text COMMENT '备注',
  `created_by` int(11) unsigned NOT NULL COMMENT '录单员ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `member_id` (`member_id`),
  KEY `status` (`status`),
  KEY `assigned_to` (`assigned_to`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `fk_customer_orders_member` FOREIGN KEY (`member_id`) REFERENCES `fs_members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户订单表';

-- 6. 客户订单商品明细表 (fs_customer_order_items)
CREATE TABLE `fs_customer_order_items` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `customer_order_id` int(11) unsigned NOT NULL COMMENT '客户订单ID',
  `product_name` varchar(200) NOT NULL COMMENT '商品名称',
  `product_sku` varchar(100) DEFAULT '' COMMENT '商品SKU',
  `product_spec` varchar(200) DEFAULT '' COMMENT '商品规格',
  `quantity` int(11) NOT NULL COMMENT '数量',
  `unit_price` decimal(8,2) DEFAULT 0.00 COMMENT '单价',
  `amount` decimal(10,2) DEFAULT 0.00 COMMENT '小计金额',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `customer_order_id` (`customer_order_id`),
  KEY `product_sku` (`product_sku`),
  CONSTRAINT `fk_customer_items_order` FOREIGN KEY (`customer_order_id`) REFERENCES `fs_customer_orders` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户订单商品明细表';

-- 7. 库存表 (fs_inventory)
CREATE TABLE `fs_inventory` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `member_id` int(11) unsigned NOT NULL COMMENT '会员ID',
  `product_name` varchar(200) NOT NULL COMMENT '商品名称',
  `product_sku` varchar(100) NOT NULL COMMENT '商品SKU',
  `product_spec` varchar(200) DEFAULT '' COMMENT '商品规格',
  `current_stock` int(11) DEFAULT 0 COMMENT '当前库存',
  `total_in` int(11) DEFAULT 0 COMMENT '总入库数量',
  `total_out` int(11) DEFAULT 0 COMMENT '总出库数量',
  `last_purchase_price` decimal(8,2) DEFAULT 0.00 COMMENT '最后进货价',
  `average_cost` decimal(8,2) DEFAULT 0.00 COMMENT '平均成本',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `member_product` (`member_id`, `product_sku`),
  KEY `member_id` (`member_id`),
  KEY `product_sku` (`product_sku`),
  CONSTRAINT `fk_inventory_member` FOREIGN KEY (`member_id`) REFERENCES `fs_members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存表';

-- 8. 财务记录表 (fs_financial_records)
CREATE TABLE `fs_financial_records` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `member_id` int(11) unsigned NOT NULL COMMENT '会员ID',
  `type` varchar(20) NOT NULL COMMENT '类型：income=收入，cost=成本，profit=利润',
  `amount` decimal(10,2) NOT NULL COMMENT '金额',
  `source_type` varchar(20) NOT NULL COMMENT '来源类型：order=订单，purchase=进货',
  `source_id` int(11) unsigned NOT NULL COMMENT '来源ID',
  `description` varchar(255) DEFAULT '' COMMENT '描述',
  `record_date` date NOT NULL COMMENT '记录日期',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `member_id` (`member_id`),
  KEY `type` (`type`),
  KEY `source_type_id` (`source_type`, `source_id`),
  KEY `record_date` (`record_date`),
  CONSTRAINT `fk_financial_records_member` FOREIGN KEY (`member_id`) REFERENCES `fs_members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='财务记录表';

-- 插入默认数据

-- 插入管理员账号
INSERT INTO `fs_members` (`id`, `parent_id`, `username`, `password`, `nickname`, `level`, `max_sub_accounts`, `status`, `created_at`) VALUES
(1, 0, 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统管理员', 2, 999, 1, NOW());

-- 插入默认模块数据
INSERT INTO `fs_member_modules` (`member_id`, `module_name`, `module_title`, `is_enabled`) VALUES
(1, 'storage', '入库管理', 1),
(1, 'sorting', '分拣管理', 1),
(1, 'order_entry', '录单管理', 1),
(1, 'finance', '财务管理', 1);
