<?php
// 验证当前脚本连接的数据库
require_once 'server/vendor/autoload.php';

use think\facade\Db;
use think\facade\Config;

try {
    // 初始化ThinkPHP
    $app = new \think\App();
    $app->initialize();
    
    echo "=== 数据库连接验证 ===\n\n";
    
    // 1. 检查配置文件
    echo "1. 检查.env配置文件:\n";
    $envContent = file_get_contents('.env');
    $lines = explode("\n", $envContent);
    foreach ($lines as $line) {
        if (strpos($line, 'DATABASE=') === 0) {
            echo "   {$line}\n";
        }
    }
    
    // 2. 检查ThinkPHP配置
    echo "\n2. 检查ThinkPHP数据库配置:\n";
    $dbConfig = Config::get('database.connections.mysql');
    echo "   hostname: " . $dbConfig['hostname'] . "\n";
    echo "   database: " . $dbConfig['database'] . "\n";
    echo "   username: " . $dbConfig['username'] . "\n";
    echo "   prefix: " . $dbConfig['prefix'] . "\n";
    
    // 3. 检查实际连接的数据库
    echo "\n3. 检查实际连接的数据库:\n";
    $currentDb = Db::query("SELECT DATABASE() as db_name")[0]['db_name'];
    echo "   当前连接的数据库: {$currentDb}\n";
    
    // 4. 检查当前数据库中的表
    echo "\n4. 当前数据库({$currentDb})中的表:\n";
    $tables = Db::query("SHOW TABLES");
    foreach ($tables as $table) {
        $tableName = array_values($table)[0];
        echo "   - {$tableName}\n";
    }
    
    // 5. 检查AI分拣相关表是否存在
    echo "\n5. 检查AI分拣相关表:\n";
    $aiTables = [
        'fs_user_modules',
        'fs_purchase_orders',
        'fs_customer_orders',
        'fs_inventory',
        'fs_financial_records'
    ];
    
    foreach ($aiTables as $tableName) {
        try {
            $exists = Db::query("SHOW TABLES LIKE '{$tableName}'");
            if (!empty($exists)) {
                echo "   ✓ {$tableName} - 存在\n";
            } else {
                echo "   ✗ {$tableName} - 不存在\n";
            }
        } catch (\Exception $e) {
            echo "   ✗ {$tableName} - 检查失败\n";
        }
    }
    
    // 6. 检查fs_user表的AI分拣字段
    echo "\n6. 检查fs_user表的AI分拣扩展字段:\n";
    try {
        $columns = Db::query("SHOW COLUMNS FROM fs_user");
        $aiFields = ['parent_id', 'level', 'hierarchy_path', 'expire_time', 'is_ai_member'];
        
        foreach ($aiFields as $field) {
            $found = false;
            foreach ($columns as $column) {
                if ($column['Field'] === $field) {
                    $found = true;
                    break;
                }
            }
            if ($found) {
                echo "   ✓ {$field} - 已添加\n";
            } else {
                echo "   ✗ {$field} - 未添加\n";
            }
        }
    } catch (\Exception $e) {
        echo "   ✗ 检查fs_user表失败: " . $e->getMessage() . "\n";
    }
    
    // 7. 检查用户数据
    echo "\n7. 检查用户数据:\n";
    try {
        $users = Db::table('fs_user')->select();
        echo "   用户总数: " . count($users) . "\n";
        foreach ($users as $user) {
            $aiMember = isset($user['is_ai_member']) ? $user['is_ai_member'] : 'N/A';
            echo "   - ID:{$user['id']}, 用户名:{$user['username']}, AI会员:{$aiMember}\n";
        }
    } catch (\Exception $e) {
        echo "   ✗ 检查用户数据失败: " . $e->getMessage() . "\n";
    }
    
    // 8. 创建缺失的表结构
    echo "\n8. 创建AI分拣系统用户体系:\n";

    // 检查并创建fs_user_sub_accounts表
    try {
        $exists = Db::query("SHOW TABLES LIKE 'fs_user_sub_accounts'");
        if (empty($exists)) {
            echo "   创建fs_user_sub_accounts表...\n";
            $sql = "CREATE TABLE `fs_user_sub_accounts` (
              `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '子账号ID',
              `parent_user_id` int(11) NOT NULL COMMENT '主会员ID，关联fs_user.id',
              `username` varchar(50) NOT NULL COMMENT '子账号用户名',
              `password` varchar(255) NOT NULL COMMENT '密码',
              `nickname` varchar(100) DEFAULT '' COMMENT '昵称',
              `phone` varchar(20) DEFAULT '' COMMENT '手机号',
              `email` varchar(100) DEFAULT '' COMMENT '邮箱',
              `avatar` varchar(255) DEFAULT '' COMMENT '头像',
              `account_type` varchar(20) NOT NULL COMMENT '账号类型：storage=入库员，sorting=分拣员，order_entry=录单员，finance=财务员',
              `status` tinyint(1) DEFAULT 1 COMMENT '状态：0=禁用，1=启用',
              `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
              `last_login_ip` varchar(50) DEFAULT '' COMMENT '最后登录IP',
              `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
              `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
              PRIMARY KEY (`id`),
              UNIQUE KEY `username` (`username`),
              KEY `parent_user_id` (`parent_user_id`),
              KEY `account_type` (`account_type`),
              KEY `status` (`status`),
              CONSTRAINT `fk_sub_accounts_user` FOREIGN KEY (`parent_user_id`) REFERENCES `fs_user` (`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户子账号表'";

            Db::execute($sql);
            echo "   ✓ fs_user_sub_accounts表创建成功\n";
        } else {
            echo "   ✓ fs_user_sub_accounts表已存在\n";
        }
    } catch (\Exception $e) {
        echo "   ✗ 创建fs_user_sub_accounts表失败: " . $e->getMessage() . "\n";
    }

    // 检查并为fs_user表添加AI分拣字段
    echo "   为fs_user表添加AI分拣字段...\n";
    $aiFields = [
        'max_sub_accounts' => 'int(11) DEFAULT 0 COMMENT \'最大子账号数量\'',
        'current_sub_accounts' => 'int(11) DEFAULT 0 COMMENT \'当前子账号数量\'',
        'expire_time' => 'datetime DEFAULT NULL COMMENT \'到期时间\'',
        'is_ai_member' => 'tinyint(1) DEFAULT 0 COMMENT \'是否AI分拣会员:1是,0否\''
    ];

    foreach ($aiFields as $fieldName => $fieldDef) {
        try {
            $columns = Db::query("SHOW COLUMNS FROM fs_user LIKE '{$fieldName}'");
            if (empty($columns)) {
                Db::execute("ALTER TABLE `fs_user` ADD COLUMN `{$fieldName}` {$fieldDef} AFTER `status`");
                echo "   ✓ 添加字段 {$fieldName}\n";
            } else {
                echo "   ✓ 字段 {$fieldName} 已存在\n";
            }
        } catch (\Exception $e) {
            echo "   ✗ 添加字段 {$fieldName} 失败: " . $e->getMessage() . "\n";
        }
    }

    // 9. 总结
    echo "\n=== 总结 ===\n";
    if ($currentDb === 'aifen') {
        echo "✓ 脚本正确连接到 aifen 数据库\n";
    } else {
        echo "❌ 脚本连接到错误的数据库: {$currentDb}，应该连接 aifen\n";
    }
    
} catch (\Exception $e) {
    echo "❌ 验证失败: " . $e->getMessage() . "\n";
}
