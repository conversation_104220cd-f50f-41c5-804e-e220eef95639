<?php
// 验证当前脚本连接的数据库
require_once 'vendor/autoload.php';

use think\facade\Db;
use think\facade\Config;

try {
    // 初始化ThinkPHP
    $app = new \think\App();
    $app->initialize();
    
    echo "=== 数据库连接验证 ===\n\n";
    
    // 1. 检查配置文件
    echo "1. 检查.env配置文件:\n";
    $envContent = file_get_contents('.env');
    $lines = explode("\n", $envContent);
    foreach ($lines as $line) {
        if (strpos($line, 'DATABASE=') === 0) {
            echo "   {$line}\n";
        }
    }
    
    // 2. 检查ThinkPHP配置
    echo "\n2. 检查ThinkPHP数据库配置:\n";
    $dbConfig = Config::get('database.connections.mysql');
    echo "   hostname: " . $dbConfig['hostname'] . "\n";
    echo "   database: " . $dbConfig['database'] . "\n";
    echo "   username: " . $dbConfig['username'] . "\n";
    echo "   prefix: " . $dbConfig['prefix'] . "\n";
    
    // 3. 检查实际连接的数据库
    echo "\n3. 检查实际连接的数据库:\n";
    $currentDb = Db::query("SELECT DATABASE() as db_name")[0]['db_name'];
    echo "   当前连接的数据库: {$currentDb}\n";
    
    // 4. 检查当前数据库中的表
    echo "\n4. 当前数据库({$currentDb})中的表:\n";
    $tables = Db::query("SHOW TABLES");
    foreach ($tables as $table) {
        $tableName = array_values($table)[0];
        echo "   - {$tableName}\n";
    }
    
    // 5. 检查AI分拣相关表是否存在
    echo "\n5. 检查AI分拣相关表:\n";
    $aiTables = [
        'fs_user_modules',
        'fs_purchase_orders',
        'fs_customer_orders',
        'fs_inventory',
        'fs_financial_records'
    ];
    
    foreach ($aiTables as $tableName) {
        try {
            $exists = Db::query("SHOW TABLES LIKE '{$tableName}'");
            if (!empty($exists)) {
                echo "   ✓ {$tableName} - 存在\n";
            } else {
                echo "   ✗ {$tableName} - 不存在\n";
            }
        } catch (\Exception $e) {
            echo "   ✗ {$tableName} - 检查失败\n";
        }
    }
    
    // 6. 检查fs_user表的AI分拣字段
    echo "\n6. 检查fs_user表的AI分拣扩展字段:\n";
    try {
        $columns = Db::query("SHOW COLUMNS FROM fs_user");
        $aiFields = ['parent_id', 'level', 'hierarchy_path', 'expire_time', 'is_ai_member'];
        
        foreach ($aiFields as $field) {
            $found = false;
            foreach ($columns as $column) {
                if ($column['Field'] === $field) {
                    $found = true;
                    break;
                }
            }
            if ($found) {
                echo "   ✓ {$field} - 已添加\n";
            } else {
                echo "   ✗ {$field} - 未添加\n";
            }
        }
    } catch (\Exception $e) {
        echo "   ✗ 检查fs_user表失败: " . $e->getMessage() . "\n";
    }
    
    // 7. 检查用户数据
    echo "\n7. 检查用户数据:\n";
    try {
        $users = Db::table('fs_user')->select();
        echo "   用户总数: " . count($users) . "\n";
        foreach ($users as $user) {
            $aiMember = isset($user['is_ai_member']) ? $user['is_ai_member'] : 'N/A';
            echo "   - ID:{$user['id']}, 用户名:{$user['username']}, AI会员:{$aiMember}\n";
        }
    } catch (\Exception $e) {
        echo "   ✗ 检查用户数据失败: " . $e->getMessage() . "\n";
    }
    
    // 8. 总结
    echo "\n=== 总结 ===\n";
    if ($currentDb === 'aifen') {
        echo "✓ 脚本正确连接到 aifen 数据库\n";
    } else {
        echo "❌ 脚本连接到错误的数据库: {$currentDb}，应该连接 aifen\n";
    }
    
} catch (\Exception $e) {
    echo "❌ 验证失败: " . $e->getMessage() . "\n";
}
