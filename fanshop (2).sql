-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version phpStudy 2014
-- http://www.phpmyadmin.net
--
-- 主机: localhost
-- 生成日期: 
-- 服务器版本: 5.7.26
-- PHP 版本: 7.3.4

SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;

--
-- 数据库: `fanshop`
--

-- --------------------------------------------------------

--
-- 表的结构 `fs_admin`
--

CREATE TABLE IF NOT EXISTS `fs_admin` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态:1正常,2禁用',
  `last_login_time` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='管理员表' AUTO_INCREMENT=3 ;

--
-- 转存表中的数据 `fs_admin`
--

INSERT INTO `fs_admin` (`id`, `username`, `password`, `nickname`, `avatar`, `email`, `phone`, `status`, `last_login_time`, `last_login_ip`, `created_at`, `updated_at`) VALUES
(1, 'admin', '$2y$10$lkVlEAdj11wIRfjgYNsTmedEiSMI9RAtPAamE0G726c.HZQQV.lR2', '超级管理员', 'uploads\\avatar\\admin_avatar.jpg', '<EMAIL>', '', 1, '2025-07-31 00:56:43', '127.0.0.1', '2025-07-25 11:22:27', '2025-07-31 00:56:43'),
(2, 'ruishengkj', '$2y$10$p7h4uKDksbsz9FApo3QUiOMc4.MbTrLQZYb/dVJDn4weIvAaKW9dK', '睿胜科技', '', '<EMAIL>', '15666666666', 1, '2025-07-26 10:12:27', '127.0.0.1', '2025-07-26 09:36:43', '2025-07-26 16:34:40');

-- --------------------------------------------------------

--
-- 表的结构 `fs_admin_role`
--

CREATE TABLE IF NOT EXISTS `fs_admin_role` (
  `admin_id` int(11) NOT NULL,
  `role_id` int(11) NOT NULL,
  PRIMARY KEY (`admin_id`,`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员角色关联表';

--
-- 转存表中的数据 `fs_admin_role`
--

INSERT INTO `fs_admin_role` (`admin_id`, `role_id`) VALUES
(1, 1),
(2, 2);

-- --------------------------------------------------------

--
-- 表的结构 `fs_articles`
--

CREATE TABLE IF NOT EXISTS `fs_articles` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '文章ID',
  `title` varchar(200) NOT NULL COMMENT '文章标题',
  `content` longtext COMMENT '文章内容',
  `summary` text COMMENT '文章简介',
  `category_id` int(11) unsigned DEFAULT NULL COMMENT '分类ID',
  `tags` varchar(500) DEFAULT NULL COMMENT '标签（逗号分隔）',
  `cover_image` varchar(500) DEFAULT NULL COMMENT '封面图片URL',
  `author_id` int(11) unsigned DEFAULT NULL COMMENT '作者ID',
  `author_name` varchar(100) DEFAULT NULL COMMENT '作者姓名',
  `is_visible` tinyint(1) NOT NULL DEFAULT '1' COMMENT '显示状态：0=隐藏，1=显示',
  `view_count` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '浏览次数',
  `like_count` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '点赞次数',
  `published_at` datetime DEFAULT NULL COMMENT '发布时间',
  `is_published` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否发布：0=草稿，1=已发布',
  `is_featured` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否推荐：0=否，1=是',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1=启用，2=禁用',
  `sort` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '排序（数值越大越靠前）',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_author_id` (`author_id`),
  KEY `idx_is_published` (`is_published`),
  KEY `idx_status` (`status`),
  KEY `idx_published_at` (`published_at`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_is_visible` (`is_visible`),
  KEY `idx_sort` (`sort`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COMMENT='文章表' AUTO_INCREMENT=13 ;

--
-- 转存表中的数据 `fs_articles`
--

INSERT INTO `fs_articles` (`id`, `title`, `content`, `summary`, `category_id`, `tags`, `cover_image`, `author_id`, `author_name`, `is_visible`, `view_count`, `like_count`, `published_at`, `is_published`, `is_featured`, `status`, `sort`, `created_at`, `updated_at`, `deleted_at`) VALUES
(4, '测试', '<p>1</p>', '1', 3, NULL, 'storage/uploads/20250729\\03c9ffbde04f07a2f59eb82a78035910.png', NULL, 'admin', 1, 0, 0, '2025-07-28 17:14:11', 1, 1, 1, 2, NULL, NULL, NULL),
(5, '测试1', '<p>1</p>', '1', 2, NULL, 'storage/uploads/20250729\\aebe32b662830b895b801eb57cef2853.jpg', NULL, 'admin', 1, 0, 0, '2025-07-28 17:14:27', 1, 1, 1, 2, NULL, NULL, NULL),
(8, 'dwhja', '<p>111</p>', '11', NULL, NULL, 'storage/uploads/20250729\\aebe32b662830b895b801eb57cef2853.jpg', NULL, 'asd', 1, 0, 0, '2025-07-28 17:28:01', 1, 1, 1, 1, NULL, NULL, NULL),
(11, '测试文章标题', '<p>这是测试文章的内容，包含了<strong>富文本</strong>格式。</p><p>用于验证编辑器功能是否正常。</p>', '这是一篇测试文章的简介，用于验证新的表单字段是否正常工作。', 1, NULL, 'storage/uploads/20250729\\aebe32b662830b895b801eb57cef2853.jpg', NULL, '测试作者', 1, 0, 0, NULL, 0, 0, 1, 10, '2025-07-29 17:39:41', '2025-07-29 17:39:41', NULL),
(12, '测告诉挑选', '<p><img src="http://fanshop.gg/storage/uploads/20250726/ce21c1279166e9e5ed86344611839395.png" alt="未标题-1.png" data-href="" style=""/></p>', 'asd', 1, NULL, 'storage/uploads/20250729/3cc32a8304c0fef7c5d8c90646ae4965.png', NULL, 'admin', 1, 0, 0, NULL, 0, 1, 1, 0, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- 表的结构 `fs_categories`
--

CREATE TABLE IF NOT EXISTS `fs_categories` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '分类名称',
  `slug` varchar(50) NOT NULL DEFAULT '' COMMENT '分类别名',
  `description` varchar(500) DEFAULT '' COMMENT '分类描述',
  `parent_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '上级分类ID',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0=禁用，1=启用',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort` (`sort`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='文章分类表' AUTO_INCREMENT=8 ;

--
-- 转存表中的数据 `fs_categories`
--

INSERT INTO `fs_categories` (`id`, `name`, `slug`, `description`, `parent_id`, `sort`, `status`, `created_at`, `updated_at`) VALUES
(1, '技术文章', 'tech', '技术相关文章分类', 0, 1, 1, '2025-07-29 08:26:13', '2025-07-29 08:26:13'),
(2, '产品介绍', 'product', '产品介绍文章分类', 0, 2, 1, '2025-07-29 08:26:13', '2025-07-30 17:26:33'),
(3, '公司动态', 'news', '公司新闻动态分类', 0, 3, 1, '2025-07-29 08:26:13', '2025-07-29 08:26:13'),
(7, '厕刷手动', '', '', 0, 0, 1, '2025-07-30 09:54:20', '2025-07-30 16:38:34');

-- --------------------------------------------------------

--
-- 表的结构 `fs_media_category`
--

CREATE TABLE IF NOT EXISTS `fs_media_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `parent_id` int(11) DEFAULT '0' COMMENT '父分类ID',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='媒体文件分类表' AUTO_INCREMENT=5 ;

--
-- 转存表中的数据 `fs_media_category`
--

INSERT INTO `fs_media_category` (`id`, `name`, `parent_id`, `sort`, `created_at`, `updated_at`) VALUES
(1, 'logo', 0, 1, '2025-07-26 13:36:08', '2025-07-26 13:36:08'),
(2, '商品图片', 0, 2, '2025-07-26 13:36:08', '2025-07-26 13:36:08'),
(3, '清报', 0, 3, '2025-07-26 13:36:08', '2025-07-26 13:36:08'),
(4, '系统图片', 0, 4, '2025-07-26 13:36:08', '2025-07-26 13:36:08');

-- --------------------------------------------------------

--
-- 表的结构 `fs_menu`
--

CREATE TABLE IF NOT EXISTS `fs_menu` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `parent_id` int(11) DEFAULT '0' COMMENT '父级ID',
  `name` varchar(50) NOT NULL COMMENT '菜单名称',
  `path` varchar(255) DEFAULT NULL COMMENT '路由路径',
  `component` varchar(255) DEFAULT NULL COMMENT '组件路径',
  `title` varchar(50) NOT NULL COMMENT '菜单标题',
  `icon` varchar(100) DEFAULT NULL COMMENT '菜单图标',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `type` tinyint(1) DEFAULT '1' COMMENT '类型:1菜单,2按钮',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态:1显示,0隐藏',
  `keep_alive` tinyint(1) DEFAULT '0' COMMENT '是否缓存',
  `external_link` varchar(255) DEFAULT NULL COMMENT '外部链接',
  `is_iframe` tinyint(1) DEFAULT '0' COMMENT '是否iframe',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='菜单表' AUTO_INCREMENT=16 ;

--
-- 转存表中的数据 `fs_menu`
--

INSERT INTO `fs_menu` (`id`, `parent_id`, `name`, `path`, `component`, `title`, `icon`, `sort`, `type`, `status`, `keep_alive`, `external_link`, `is_iframe`, `created_at`, `updated_at`) VALUES
(1, 0, 'Dashboard', '/dashboard', '/index/index', '仪表盘', '&#xe721;', 1, 1, 1, 0, NULL, 0, '2025-07-25 11:22:27', '2025-07-25 16:08:53'),
(2, 0, 'System', '/system', '/index/index', '系统管理', '&#xe7b9;', 2, 1, 1, 0, NULL, 0, '2025-07-25 11:22:27', '2025-07-25 16:08:53'),
(3, 10, 'user-list', '/user/list', '/user/list', '用户列表', '&#xe608;', 1, 1, 1, 0, NULL, 0, '2025-07-25 11:22:27', '2025-07-29 01:18:51'),
(4, 2, 'RoleManage', '/system/role', '/system/role/index', '角色管理', '&#xe817;', 2, 1, 1, 0, NULL, 0, '2025-07-25 11:22:27', '2025-07-30 17:04:25'),
(5, 2, 'MenuManage', '/system/menu', '/system/menu/index', '菜单管理', '&#xe651;', 3, 1, 1, 0, NULL, 0, '2025-07-25 11:22:27', '2025-07-26 17:21:46'),
(6, 2, 'settings', '/system/settings', '/system/settings', '系统配置', '&#xe755;', 4, 1, 1, 0, NULL, 0, '2025-07-25 11:22:27', '2025-07-26 17:21:54'),
(8, 1, 'Console', '/dashboard/console', '/dashboard/console', '工作台', '&#xe6b4;', 1, 1, 1, 0, '', 0, '2025-07-26 03:03:17', '2025-07-26 03:03:59'),
(9, 2, 'admin', '/system/admin', '/system/admin', '管理员', '&#xe73a;', 1, 1, 1, 0, '', 0, '2025-07-26 09:03:11', '2025-07-28 07:17:39'),
(10, 0, 'user', '/user', '/index/index', '用户管理', '&#xe724;', 1, 1, 1, 0, '', 0, '2025-07-26 09:42:40', '2025-07-29 01:21:17'),
(12, 2, 'article', '/system/article', '', '文章管理', '&#xe648;', 1, 1, 1, 0, NULL, 0, '2025-07-29 00:28:25', '2025-07-29 03:10:58'),
(13, 12, 'article-list', '/system/article/list', '/system/article/list/index', '文章列表', '&#xe686;', 1, 1, 1, 0, NULL, 0, '2025-07-29 00:28:25', '2025-07-29 02:28:11'),
(14, 12, 'category', '/system/article/category', '/system/article/category/index', '文章分类', '&#xe81a;', 1, 1, 1, 0, NULL, 0, '2025-07-29 00:28:25', '2025-07-29 02:28:11'),
(15, 12, 'notice', '/system/article/notice', '/system/article/notice/index', '公告管理', '&#xe64a;', 1, 1, 1, 0, NULL, 0, '2025-07-29 00:28:25', '2025-07-29 02:28:11');

-- --------------------------------------------------------

--
-- 表的结构 `fs_notices`
--

CREATE TABLE IF NOT EXISTS `fs_notices` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '公告ID',
  `title` varchar(200) NOT NULL DEFAULT '' COMMENT '公告标题',
  `content` text NOT NULL COMMENT '公告内容',
  `cover_image` varchar(255) DEFAULT '' COMMENT '封面图片',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '公告类型：1=系统通知，2=功能更新，3=服务公告',
  `is_published` tinyint(1) NOT NULL DEFAULT '0' COMMENT '发布状态：0=草稿，1=已发布',
  `is_top` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否置顶：0=否，1=是',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0=禁用，1=启用',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `view_count` int(11) NOT NULL DEFAULT '0' COMMENT '浏览量',
  `published_at` datetime DEFAULT NULL COMMENT '发布时间',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_is_published` (`is_published`),
  KEY `idx_is_top` (`is_top`),
  KEY `idx_status` (`status`),
  KEY `idx_sort` (`sort`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='公告表' AUTO_INCREMENT=6 ;

--
-- 转存表中的数据 `fs_notices`
--

INSERT INTO `fs_notices` (`id`, `title`, `content`, `cover_image`, `type`, `is_published`, `is_top`, `status`, `sort`, `view_count`, `published_at`, `created_at`, `updated_at`) VALUES
(1, '系统维护通知', '系统将于今晚22:00-24:00进行维护，期间可能无法正常访问，请提前做好准备。', '', 1, 1, 1, 1, 10, 156, '2025-07-29 08:26:13', '2025-07-29 08:26:13', '2025-07-29 08:26:13'),
(2, '新功能上线公告', '我们很高兴地宣布，新的文章管理功能已经上线，欢迎大家使用！', '', 2, 1, 0, 1, 5, 89, '2025-07-29 08:26:13', '2025-07-29 08:26:13', '2025-07-29 08:26:13'),
(3, '节假日服务安排', '<p>春节期间客服服务时间调整为9:00-18:00，其他时间请通过邮件联系。</p>', 'storage/uploads/20250729/aebe32b662830b895b801eb57cef2853.jpg', 3, 0, 0, 1, 0, 0, NULL, '2025-07-29 08:26:13', '2025-07-30 16:26:57'),
(5, '123', '<p><img src="/storage/uploads/20250729/3cc32a8304c0fef7c5d8c90646ae4965.png" alt="CD21D11C17BCA9E6ED60106EEC19F025.png" data-href="" style=""/></p>', 'storage/uploads/20250729/3cc32a8304c0fef7c5d8c90646ae4965.png', 1, 1, 0, 1, 0, 0, '2025-07-30 11:11:40', '2025-07-30 11:11:40', '2025-07-30 11:11:40');

-- --------------------------------------------------------

--
-- 表的结构 `fs_permission`
--

CREATE TABLE IF NOT EXISTS `fs_permission` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `menu_id` int(11) NOT NULL COMMENT '菜单ID',
  `name` varchar(50) NOT NULL COMMENT '权限名称',
  `code` varchar(50) NOT NULL COMMENT '权限编码',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `menu_id` (`menu_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='权限表' AUTO_INCREMENT=13 ;

--
-- 转存表中的数据 `fs_permission`
--

INSERT INTO `fs_permission` (`id`, `menu_id`, `name`, `code`, `created_at`) VALUES
(1, 3, '查看用户', 'user:view', '2025-07-25 11:22:27'),
(2, 3, '添加用户', 'user:add', '2025-07-25 11:22:27'),
(3, 3, '编辑用户', 'user:edit', '2025-07-25 11:22:27'),
(4, 3, '删除用户', 'user:delete', '2025-07-25 11:22:27'),
(5, 4, '查看角色', 'role:view', '2025-07-25 11:22:27'),
(6, 4, '添加角色', 'role:add', '2025-07-25 11:22:27'),
(7, 4, '编辑角色', 'role:edit', '2025-07-25 11:22:27'),
(8, 4, '删除角色', 'role:delete', '2025-07-25 11:22:27'),
(9, 5, '查看菜单', 'menu:view', '2025-07-25 11:22:27'),
(10, 5, '添加菜单', 'menu:add', '2025-07-25 11:22:27'),
(11, 5, '编辑菜单', 'menu:edit', '2025-07-25 11:22:27'),
(12, 5, '删除菜单', 'menu:delete', '2025-07-25 11:22:27');

-- --------------------------------------------------------

--
-- 表的结构 `fs_role`
--

CREATE TABLE IF NOT EXISTS `fs_role` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `role_name` varchar(50) NOT NULL COMMENT '角色名称',
  `role_code` varchar(50) NOT NULL COMMENT '角色编码',
  `description` text COMMENT '角色描述',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态:1启用,0禁用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `role_code` (`role_code`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='角色表' AUTO_INCREMENT=3 ;

--
-- 转存表中的数据 `fs_role`
--

INSERT INTO `fs_role` (`id`, `role_name`, `role_code`, `description`, `status`, `created_at`, `updated_at`) VALUES
(1, '超级管理员', 'R_SUPER', '拥有所有权限的超级管理员', 1, '2025-07-25 11:22:27', '2025-07-25 11:22:27'),
(2, '系统管理员', 'R_ADMIN', '系统管理员角色', 1, '2025-07-25 11:22:27', '2025-07-25 11:22:27');

-- --------------------------------------------------------

--
-- 表的结构 `fs_role_menu`
--

CREATE TABLE IF NOT EXISTS `fs_role_menu` (
  `role_id` int(11) NOT NULL,
  `menu_id` int(11) NOT NULL,
  PRIMARY KEY (`role_id`,`menu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色菜单关联表';

--
-- 转存表中的数据 `fs_role_menu`
--

INSERT INTO `fs_role_menu` (`role_id`, `menu_id`) VALUES
(1, 1),
(1, 2),
(1, 3),
(1, 4),
(1, 5),
(1, 6);

-- --------------------------------------------------------

--
-- 表的结构 `fs_role_permission`
--

CREATE TABLE IF NOT EXISTS `fs_role_permission` (
  `role_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL,
  PRIMARY KEY (`role_id`,`permission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限关联表';

--
-- 转存表中的数据 `fs_role_permission`
--

INSERT INTO `fs_role_permission` (`role_id`, `permission_id`) VALUES
(1, 1),
(1, 2),
(1, 3),
(1, 4),
(1, 5),
(1, 6),
(1, 7),
(1, 8),
(1, 9),
(1, 10),
(1, 11),
(1, 12);

-- --------------------------------------------------------

--
-- 表的结构 `fs_system_config`
--

CREATE TABLE IF NOT EXISTS `fs_system_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `config_desc` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `config_type` varchar(20) DEFAULT 'string' COMMENT '配置类型',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `config_key` (`config_key`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表' AUTO_INCREMENT=15 ;

--
-- 转存表中的数据 `fs_system_config`
--

INSERT INTO `fs_system_config` (`id`, `config_key`, `config_value`, `config_desc`, `config_type`, `created_at`, `updated_at`) VALUES
(1, 'site_name', 'fanshop', '网站名称', 'string', '2025-07-25 11:22:27', '2025-07-26 14:55:06'),
(2, 'site_logo', 'storage/uploads/20250726/ce21c1279166e9e5ed86344611839395.png', '网站Logo', 'string', '2025-07-25 11:22:27', '2025-07-27 11:24:34'),
(3, 'upload_max_size', '10485760', '上传文件最大大小(字节)', 'number', '2025-07-25 11:22:27', '2025-07-25 11:22:27'),
(4, 'upload_allowed_ext', 'jpg,jpeg,png,gif,webp,pdf,doc,docx,xls,xlsx', '允许上传的文件扩展名', 'string', '2025-07-25 11:22:27', '2025-07-25 11:22:27'),
(5, 'site_description', '', '网站描述', 'string', '2025-07-26 11:41:11', '2025-07-26 14:48:15'),
(6, 'site_keywords', '', '网站关键词', 'string', '2025-07-26 11:41:11', '2025-07-26 14:48:15'),
(7, 'site_favicon', '', '网站图标', 'string', '2025-07-26 11:41:11', '2025-07-26 11:41:11'),
(8, 'site_copyright', '', '版权信息', 'string', '2025-07-26 11:41:11', '2025-07-26 14:48:15'),
(9, 'site_icp', '', 'ICP备案号', 'string', '2025-07-26 11:41:11', '2025-07-26 11:41:11'),
(10, 'site_phone', '************', '联系电话', 'string', '2025-07-26 11:41:11', '2025-07-26 11:41:38'),
(11, 'site_email', '', '联系邮箱', 'string', '2025-07-26 11:41:11', '2025-07-26 11:41:11'),
(12, 'site_address', '', '联系地址', 'string', '2025-07-26 11:41:11', '2025-07-26 11:41:11'),
(13, 'logo_size', '100', '', 'number', '2025-07-26 15:24:01', '2025-07-26 15:24:01'),
(14, 'file_url_full', '', '文件URL格式：false=相对路径，true=完整域名', 'boolean', '2025-07-29 10:48:12', '2025-07-29 10:48:12');

-- --------------------------------------------------------

--
-- 表的结构 `fs_upload`
--

CREATE TABLE IF NOT EXISTS `fs_upload` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `original_name` varchar(255) NOT NULL COMMENT '原始文件名',
  `file_name` varchar(255) NOT NULL COMMENT '存储文件名',
  `file_path` varchar(500) NOT NULL COMMENT '文件路径',
  `file_url` varchar(500) DEFAULT NULL COMMENT '文件访问URL',
  `file_size` bigint(20) DEFAULT NULL COMMENT '文件大小',
  `file_type` varchar(50) DEFAULT NULL COMMENT '文件类型',
  `mime_type` varchar(100) DEFAULT NULL COMMENT 'MIME类型',
  `category_id` int(11) DEFAULT '0' COMMENT '分类ID',
  `upload_user_id` int(11) DEFAULT NULL COMMENT '上传用户ID',
  `upload_user_type` varchar(20) DEFAULT 'admin' COMMENT '上传用户类型:admin,user',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_file_type` (`file_type`),
  KEY `idx_upload_user` (`upload_user_id`,`upload_user_type`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='文件上传表' AUTO_INCREMENT=9 ;

--
-- 转存表中的数据 `fs_upload`
--

INSERT INTO `fs_upload` (`id`, `original_name`, `file_name`, `file_path`, `file_url`, `file_size`, `file_type`, `mime_type`, `category_id`, `upload_user_id`, `upload_user_type`, `created_at`, `updated_at`) VALUES
(5, '未标题-1.png', 'ce21c1279166e9e5ed86344611839395.png', 'storage/uploads/20250726/ce21c1279166e9e5ed86344611839395.png', NULL, 19550, 'png', 'image/png', 0, 1, 'admin', '2025-07-26 15:07:28', '2025-07-29 11:39:59'),
(6, 'b_504bcf9d9597e60a495bd5dbaf2ffdb0.jpg', 'aebe32b662830b895b801eb57cef2853.jpg', 'storage/uploads/20250729/aebe32b662830b895b801eb57cef2853.jpg', NULL, 24140, 'jpg', 'image/jpeg', 0, 1, 'admin', '2025-07-29 09:01:52', '2025-07-29 11:39:54'),
(7, '66a3b590116df.png', '03c9ffbde04f07a2f59eb82a78035910.png', 'storage/uploads/20250729/03c9ffbde04f07a2f59eb82a78035910.png', NULL, 7457, 'png', 'image/png', 0, 1, 'admin', '2025-07-29 11:12:01', '2025-07-29 11:39:49'),
(8, 'CD21D11C17BCA9E6ED60106EEC19F025.png', '3cc32a8304c0fef7c5d8c90646ae4965.png', 'storage/uploads/20250729/3cc32a8304c0fef7c5d8c90646ae4965.png', NULL, 406001, 'png', 'image/png', 0, 1, 'admin', '2025-07-29 11:38:19', '2025-07-29 11:38:19');

-- --------------------------------------------------------

--
-- 表的结构 `fs_user`
--

CREATE TABLE IF NOT EXISTS `fs_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `pay_password` varchar(255) DEFAULT '' COMMENT '支付密码',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `referrer_id` int(11) DEFAULT '0' COMMENT '推荐人ID',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态:1正常,2禁用,3注销',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='用户表' AUTO_INCREMENT=8 ;

--
-- 转存表中的数据 `fs_user`
--

INSERT INTO `fs_user` (`id`, `username`, `password`, `pay_password`, `nickname`, `avatar`, `email`, `phone`, `referrer_id`, `birthday`, `status`, `created_at`, `updated_at`) VALUES
(1, 'user_17538755258032', '$2y$10$LEQFq5YAPkAd/Lp3XfDD5uE9tJsYj0KeYs/9RuncvhPzpzJ0148Gq', '$2y$10$nWf/VucA8k7J8Srwgzm9CexpxF94mWgSu4hx26lnQmkNAxoQg3Sf2', 'asd', 'storage/uploads/20250729/aebe32b662830b895b801eb57cef2853.jpg', '', '15666666666', 2, NULL, 1, '2025-07-30 11:38:45', '2025-07-30 15:43:36'),
(7, 'user_17538904737234', '$2y$10$r6bmUTKsmAvL/F8eb2P8Xu7RvDnwFG8wEpaUB2ty9tLPtbqBYB51S', '$2y$10$MIi7l26plt85nsTYQUAn8.pauY3LZ5xYLBm8tD3AKYm1vOdj1fwgu', 'sdaa', 'storage/uploads/20250729/3cc32a8304c0fef7c5d8c90646ae4965.png', '', '18888888888', 1, NULL, 1, '2025-07-30 15:47:54', '2025-07-30 15:47:54');

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
