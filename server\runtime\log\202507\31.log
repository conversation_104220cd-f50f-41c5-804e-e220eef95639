[2025-07-31T09:59:27+08:00][sql] CONNECT:[ UseTime:0.002879s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-31T09:59:27+08:00][sql] SHOW TABLES LIKE 'fs_members' [ RunTime:0.000943s ]
[2025-07-31T09:59:27+08:00][sql] SHOW TABLES LIKE 'fs_member_modules' [ RunTime:0.001044s ]
[2025-07-31T09:59:27+08:00][sql] SHOW TABLES LIKE 'fs_purchase_orders' [ RunTime:0.001071s ]
[2025-07-31T09:59:27+08:00][sql] SHOW TABLES LIKE 'fs_purchase_order_items' [ RunTime:0.001042s ]
[2025-07-31T09:59:27+08:00][sql] SHOW TABLES LIKE 'fs_customer_orders' [ RunTime:0.000950s ]
[2025-07-31T09:59:27+08:00][sql] SHOW TABLES LIKE 'fs_customer_order_items' [ RunTime:0.001091s ]
[2025-07-31T09:59:27+08:00][sql] SHOW TABLES LIKE 'fs_inventory' [ RunTime:0.000973s ]
[2025-07-31T09:59:27+08:00][sql] SHOW TABLES LIKE 'fs_financial_records' [ RunTime:0.001101s ]
[2025-07-31T10:04:30+08:00][sql] CONNECT:[ UseTime:0.002331s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-31T10:04:30+08:00][sql] CREATE TABLE `fs_members` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '会员ID',
  `parent_id` int(11) unsigned DEFAULT 0 COMMENT '上级会员ID，0表示管理员直接开通',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `nickname` varchar(100) DEFAULT '' COMMENT '昵称',
  `phone` varchar(20) DEFAULT '' COMMENT '手机号',
  `email` varchar(100) DEFAULT '' COMMENT '邮箱',
  `avatar` varchar(255) DEFAULT '' COMMENT '头像',
  `level` tinyint(1) DEFAULT 1 COMMENT '会员等级：1=普通会员，2=高级会员',
  `max_sub_accounts` int(11) DEFAULT 0 COMMENT '最大子账号数量',
  `current_sub_accounts` int(11) DEFAULT 0 COMMENT '当前子账号数量',
  `expire_time` datetime DEFAULT NULL COMMENT '会员到期时间',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：0=禁用，1=启用',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT '' COMMENT '最后登录IP',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  KEY `parent_id` (`parent_id`),
  KEY `status` (`status`),
  KEY `expire_time` (`expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员表' [ RunTime:0.015431s ]
[2025-07-31T10:04:30+08:00][sql] CREATE TABLE `fs_member_modules` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `member_id` int(11) unsigned NOT NULL COMMENT '会员ID',
  `module_name` varchar(50) NOT NULL COMMENT '模块名称：storage=入库，sorting=分拣，order_entry=录单，finance=财务',
  `module_title` varchar(100) NOT NULL COMMENT '模块标题',
  `is_enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用：0=禁用，1=启用',
  `expire_time` datetime DEFAULT NULL COMMENT '模块到期时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `member_module` (`member_id`, `module_name`),
  KEY `member_id` (`member_id`),
  KEY `module_name` (`module_name`),
  KEY `expire_time` (`expire_time`),
  CONSTRAINT `fk_member_modules_member` FOREIGN KEY (`member_id`) REFERENCES `fs_members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员模块权限表' [ RunTime:0.020386s ]
[2025-07-31T10:04:30+08:00][sql] CREATE TABLE `fs_purchase_orders` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '进货单ID',
  `member_id` int(11) unsigned NOT NULL COMMENT '会员ID',
  `order_no` varchar(50) NOT NULL COMMENT '进货单号',
  `supplier_name` varchar(100) NOT NULL COMMENT '供应商名称',
  `supplier_contact` varchar(100) DEFAULT '' COMMENT '供应商联系方式',
  `total_amount` decimal(10,2) DEFAULT 0.00 COMMENT '总金额',
  `total_quantity` int(11) DEFAULT 0 COMMENT '总数量',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1=待入库，2=部分入库，3=已完成',
  `remark` text COMMENT '备注',
  `created_by` int(11) unsigned NOT NULL COMMENT '创建人ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `member_id` (`member_id`),
  KEY `status` (`status`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `fk_purchase_orders_member` FOREIGN KEY (`member_id`) REFERENCES `fs_members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='进货单表' [ RunTime:0.015504s ]
[2025-07-31T10:04:30+08:00][sql] CREATE TABLE `fs_purchase_order_items` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `purchase_order_id` int(11) unsigned NOT NULL COMMENT '进货单ID',
  `product_name` varchar(200) NOT NULL COMMENT '商品名称',
  `product_sku` varchar(100) DEFAULT '' COMMENT '商品SKU',
  `product_spec` varchar(200) DEFAULT '' COMMENT '商品规格',
  `purchase_price` decimal(8,2) DEFAULT 0.00 COMMENT '进货价',
  `quantity` int(11) NOT NULL COMMENT '数量',
  `stored_quantity` int(11) DEFAULT 0 COMMENT '已入库数量',
  `amount` decimal(10,2) DEFAULT 0.00 COMMENT '小计金额',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `purchase_order_id` (`purchase_order_id`),
  KEY `product_sku` (`product_sku`),
  CONSTRAINT `fk_purchase_items_order` FOREIGN KEY (`purchase_order_id`) REFERENCES `fs_purchase_orders` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='进货单商品明细表' [ RunTime:0.013830s ]
[2025-07-31T10:04:30+08:00][sql] CREATE TABLE `fs_customer_orders` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `member_id` int(11) unsigned NOT NULL COMMENT '会员ID',
  `order_no` varchar(50) NOT NULL COMMENT '订单号',
  `customer_name` varchar(100) NOT NULL COMMENT '客户姓名',
  `customer_phone` varchar(20) NOT NULL COMMENT '客户电话',
  `customer_address` text NOT NULL COMMENT '客户地址',
  `total_amount` decimal(10,2) DEFAULT 0.00 COMMENT '订单总金额',
  `selling_price` decimal(10,2) DEFAULT 0.00 COMMENT '销售价格（管理员定价）',
  `cost_price` decimal(10,2) DEFAULT 0.00 COMMENT '成本价格',
  `profit` decimal(10,2) DEFAULT 0.00 COMMENT '利润',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1=待定价，2=待分配，3=分拣中，4=已完成，5=已取消',
  `assigned_to` int(11) unsigned DEFAULT NULL COMMENT '分配给的分拣员ID',
  `assigned_at` datetime DEFAULT NULL COMMENT '分配时间',
  `completed_at` datetime DEFAULT NULL COMMENT '完成时间',
  `remark` text COMMENT '备注',
  `created_by` int(11) unsigned NOT NULL COMMENT '录单员ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `member_id` (`member_id`),
  KEY `status` (`status`),
  KEY `assigned_to` (`assigned_to`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `fk_customer_orders_member` FOREIGN KEY (`member_id`) REFERENCES `fs_members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户订单表' [ RunTime:0.018326s ]
[2025-07-31T10:04:30+08:00][sql] CREATE TABLE `fs_customer_order_items` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `customer_order_id` int(11) unsigned NOT NULL COMMENT '客户订单ID',
  `product_name` varchar(200) NOT NULL COMMENT '商品名称',
  `product_sku` varchar(100) DEFAULT '' COMMENT '商品SKU',
  `product_spec` varchar(200) DEFAULT '' COMMENT '商品规格',
  `quantity` int(11) NOT NULL COMMENT '数量',
  `unit_price` decimal(8,2) DEFAULT 0.00 COMMENT '单价',
  `amount` decimal(10,2) DEFAULT 0.00 COMMENT '小计金额',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `customer_order_id` (`customer_order_id`),
  KEY `product_sku` (`product_sku`),
  CONSTRAINT `fk_customer_items_order` FOREIGN KEY (`customer_order_id`) REFERENCES `fs_customer_orders` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户订单商品明细表' [ RunTime:0.012799s ]
[2025-07-31T10:04:30+08:00][sql] CREATE TABLE `fs_inventory` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `member_id` int(11) unsigned NOT NULL COMMENT '会员ID',
  `product_name` varchar(200) NOT NULL COMMENT '商品名称',
  `product_sku` varchar(100) NOT NULL COMMENT '商品SKU',
  `product_spec` varchar(200) DEFAULT '' COMMENT '商品规格',
  `current_stock` int(11) DEFAULT 0 COMMENT '当前库存',
  `total_in` int(11) DEFAULT 0 COMMENT '总入库数量',
  `total_out` int(11) DEFAULT 0 COMMENT '总出库数量',
  `last_purchase_price` decimal(8,2) DEFAULT 0.00 COMMENT '最后进货价',
  `average_cost` decimal(8,2) DEFAULT 0.00 COMMENT '平均成本',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `member_product` (`member_id`, `product_sku`),
  KEY `member_id` (`member_id`),
  KEY `product_sku` (`product_sku`),
  CONSTRAINT `fk_inventory_member` FOREIGN KEY (`member_id`) REFERENCES `fs_members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存表' [ RunTime:0.013796s ]
[2025-07-31T10:04:30+08:00][sql] CREATE TABLE `fs_financial_records` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `member_id` int(11) unsigned NOT NULL COMMENT '会员ID',
  `type` varchar(20) NOT NULL COMMENT '类型：income=收入，cost=成本，profit=利润',
  `amount` decimal(10,2) NOT NULL COMMENT '金额',
  `source_type` varchar(20) NOT NULL COMMENT '来源类型：order=订单，purchase=进货',
  `source_id` int(11) unsigned NOT NULL COMMENT '来源ID',
  `description` varchar(255) DEFAULT '' COMMENT '描述',
  `record_date` date NOT NULL COMMENT '记录日期',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `member_id` (`member_id`),
  KEY `type` (`type`),
  KEY `source_type_id` (`source_type`, `source_id`),
  KEY `record_date` (`record_date`),
  CONSTRAINT `fk_financial_records_member` FOREIGN KEY (`member_id`) REFERENCES `fs_members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='财务记录表' [ RunTime:0.017433s ]
[2025-07-31T10:04:30+08:00][sql] INSERT INTO `fs_members` (`id`, `parent_id`, `username`, `password`, `nickname`, `level`, `max_sub_accounts`, `status`, `created_at`) VALUES
(1, 0, 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统管理员', 2, 999, 1, NOW()) [ RunTime:0.002878s ]
[2025-07-31T10:04:30+08:00][sql] INSERT INTO `fs_member_modules` (`member_id`, `module_name`, `module_title`, `is_enabled`) VALUES
(1, 'storage', '入库管理', 1),
(1, 'sorting', '分拣管理', 1),
(1, 'order_entry', '录单管理', 1),
(1, 'finance', '财务管理', 1) [ RunTime:0.002679s ]
[2025-07-31T10:04:30+08:00][sql] SHOW TABLES LIKE 'fs_members' [ RunTime:0.001363s ]
[2025-07-31T10:04:30+08:00][sql] SHOW TABLES LIKE 'fs_member_modules' [ RunTime:0.001263s ]
[2025-07-31T10:04:30+08:00][sql] SHOW TABLES LIKE 'fs_purchase_orders' [ RunTime:0.001267s ]
[2025-07-31T10:04:30+08:00][sql] SHOW TABLES LIKE 'fs_purchase_order_items' [ RunTime:0.001170s ]
[2025-07-31T10:04:30+08:00][sql] SHOW TABLES LIKE 'fs_customer_orders' [ RunTime:0.000693s ]
[2025-07-31T10:04:30+08:00][sql] SHOW TABLES LIKE 'fs_customer_order_items' [ RunTime:0.001144s ]
[2025-07-31T10:04:30+08:00][sql] SHOW TABLES LIKE 'fs_inventory' [ RunTime:0.000659s ]
[2025-07-31T10:04:30+08:00][sql] SHOW TABLES LIKE 'fs_financial_records' [ RunTime:0.001221s ]
[2025-07-31T10:04:30+08:00][sql] SHOW FULL COLUMNS FROM `fs_members` [ RunTime:0.001148s ]
[2025-07-31T10:04:30+08:00][sql] SELECT * FROM `fs_members` WHERE  `username` = 'admin' LIMIT 1 [ RunTime:0.000522s ]
[2025-07-31T10:04:30+08:00][sql] SHOW FULL COLUMNS FROM `fs_member_modules` [ RunTime:0.000858s ]
[2025-07-31T10:04:30+08:00][sql] SELECT * FROM `fs_member_modules` WHERE  `member_id` = 1 [ RunTime:0.000475s ]
[2025-07-31T10:05:21+08:00][sql] CONNECT:[ UseTime:0.002634s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-31T10:05:21+08:00][sql] SHOW FULL COLUMNS FROM `fs_members` [ RunTime:0.003701s ]
[2025-07-31T10:05:21+08:00][sql] UPDATE `fs_members`  SET `password` = '$2y$10$n6kAB66itjO2DnomsAxLRO7U/FIg/5AYXa4bnESRN7dyB57KVvIvW'  WHERE  `username` = 'admin' [ RunTime:0.001795s ]
[2025-07-31T10:05:21+08:00][sql] SELECT * FROM `fs_members` WHERE  `username` = 'admin' LIMIT 1 [ RunTime:0.000532s ]
[2025-07-31T10:08:27+08:00][sql] CONNECT:[ UseTime:0.002638s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-31T10:08:27+08:00][sql] SHOW TABLES [ RunTime:0.001163s ]
[2025-07-31T10:08:27+08:00][sql] SHOW FULL COLUMNS FROM `fs_admin` [ RunTime:0.004031s ]
[2025-07-31T10:08:27+08:00][sql] SELECT * FROM `fs_admin` [ RunTime:0.000657s ]
[2025-07-31T10:08:27+08:00][sql] SHOW FULL COLUMNS FROM `fs_user` [ RunTime:0.003838s ]
[2025-07-31T10:08:27+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_user` [ RunTime:0.000714s ]
[2025-07-31T10:08:27+08:00][sql] SHOW FULL COLUMNS FROM `fs_role` [ RunTime:0.003291s ]
[2025-07-31T10:08:27+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_role` [ RunTime:0.000421s ]
[2025-07-31T10:08:27+08:00][sql] SHOW FULL COLUMNS FROM `fs_menu` [ RunTime:0.002188s ]
[2025-07-31T10:08:27+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_menu` [ RunTime:0.000469s ]
[2025-07-31T10:08:27+08:00][sql] SHOW TABLES LIKE 'fs_members' [ RunTime:0.000693s ]
[2025-07-31T10:08:27+08:00][sql] SHOW TABLES LIKE 'fs_member_modules' [ RunTime:0.000724s ]
[2025-07-31T10:08:27+08:00][sql] SHOW TABLES LIKE 'fs_purchase_orders' [ RunTime:0.001055s ]
[2025-07-31T10:08:27+08:00][sql] SHOW TABLES LIKE 'fs_purchase_order_items' [ RunTime:0.000717s ]
[2025-07-31T10:08:27+08:00][sql] SHOW TABLES LIKE 'fs_customer_orders' [ RunTime:0.000647s ]
[2025-07-31T10:08:27+08:00][sql] SHOW TABLES LIKE 'fs_customer_order_items' [ RunTime:0.000657s ]
[2025-07-31T10:08:27+08:00][sql] SHOW TABLES LIKE 'fs_inventory' [ RunTime:0.000699s ]
[2025-07-31T10:08:27+08:00][sql] SHOW TABLES LIKE 'fs_financial_records' [ RunTime:0.000678s ]
[2025-07-31T10:10:19+08:00][sql] CONNECT:[ UseTime:0.002053s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-31T10:10:19+08:00][sql] DROP TABLE IF EXISTS `fs_purchase_orders` [ RunTime:0.007597s ]
[2025-07-31T10:10:19+08:00][sql] DROP TABLE IF EXISTS `fs_customer_orders` [ RunTime:0.006285s ]
[2025-07-31T10:10:19+08:00][sql] CREATE TABLE `fs_ai_members` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '会员ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `parent_id` int(11) DEFAULT 0 COMMENT '上级会员ID',
  `level` int(11) DEFAULT 1 COMMENT '会员等级',
  `hierarchy_path` varchar(500) DEFAULT '0' COMMENT '层级路径',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态:1启用,0禁用',
  `expire_time` datetime DEFAULT NULL COMMENT '到期时间',
  `created_by` int(11) DEFAULT NULL COMMENT '创建人ID(关联fs_admin.id)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI分拣系统会员表' [ RunTime:0.019525s ]
[2025-07-31T10:10:19+08:00][sql] CREATE TABLE `fs_ai_member_modules` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `member_id` int(11) NOT NULL COMMENT '会员ID',
  `module_name` varchar(50) NOT NULL COMMENT '模块名称',
  `module_title` varchar(100) NOT NULL COMMENT '模块标题',
  `is_enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用:1启用,0禁用',
  `expire_time` datetime DEFAULT NULL COMMENT '模块到期时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `member_module` (`member_id`, `module_name`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_module_name` (`module_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI分拣系统会员模块权限表' [ RunTime:0.016093s ]
[2025-07-31T10:10:19+08:00][sql] CREATE TABLE `fs_ai_purchase_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '进货单ID',
  `order_no` varchar(50) NOT NULL COMMENT '进货单号',
  `member_id` int(11) NOT NULL COMMENT '会员ID',
  `supplier_name` varchar(100) NOT NULL COMMENT '供应商名称',
  `supplier_contact` varchar(50) DEFAULT NULL COMMENT '供应商联系人',
  `supplier_phone` varchar(20) DEFAULT NULL COMMENT '供应商电话',
  `total_amount` decimal(10,2) DEFAULT 0.00 COMMENT '总金额',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态:1待入库,2已入库,3已取消',
  `remark` text COMMENT '备注',
  `created_by` int(11) NOT NULL COMMENT '创建人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI分拣系统进货单表' [ RunTime:0.018695s ]
[2025-07-31T10:10:19+08:00][sql] CREATE TABLE `fs_ai_purchase_order_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `purchase_order_id` int(11) NOT NULL COMMENT '进货单ID',
  `product_name` varchar(200) NOT NULL COMMENT '商品名称',
  `product_sku` varchar(100) DEFAULT NULL COMMENT '商品SKU',
  `product_spec` varchar(200) DEFAULT NULL COMMENT '商品规格',
  `quantity` int(11) NOT NULL COMMENT '数量',
  `unit_price` decimal(10,2) DEFAULT 0.00 COMMENT '单价',
  `amount` decimal(10,2) DEFAULT 0.00 COMMENT '小计',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_purchase_order_id` (`purchase_order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI分拣系统进货单商品明细表' [ RunTime:0.012425s ]
[2025-07-31T10:10:19+08:00][sql] CREATE TABLE `fs_ai_customer_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(50) NOT NULL COMMENT '订单号',
  `member_id` int(11) NOT NULL COMMENT '会员ID',
  `customer_name` varchar(100) NOT NULL COMMENT '客户姓名',
  `customer_phone` varchar(20) NOT NULL COMMENT '客户电话',
  `customer_address` varchar(500) DEFAULT NULL COMMENT '客户地址',
  `total_amount` decimal(10,2) DEFAULT 0.00 COMMENT '订单总金额',
  `selling_price` decimal(10,2) DEFAULT 0.00 COMMENT '销售价格',
  `cost_price` decimal(10,2) DEFAULT 0.00 COMMENT '成本价格',
  `profit` decimal(10,2) DEFAULT 0.00 COMMENT '利润',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态:1待定价,2待分配,3分拣中,4已完成,5已取消',
  `assigned_to` int(11) DEFAULT NULL COMMENT '分配给的分拣员ID',
  `assigned_at` datetime DEFAULT NULL COMMENT '分配时间',
  `remark` text COMMENT '备注',
  `created_by` int(11) NOT NULL COMMENT '创建人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_status` (`status`),
  KEY `idx_assigned_to` (`assigned_to`),
  KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI分拣系统客户订单表' [ RunTime:0.016827s ]
[2025-07-31T10:10:19+08:00][sql] CREATE TABLE `fs_ai_customer_order_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `customer_order_id` int(11) NOT NULL COMMENT '客户订单ID',
  `product_name` varchar(200) NOT NULL COMMENT '商品名称',
  `product_sku` varchar(100) DEFAULT NULL COMMENT '商品SKU',
  `product_spec` varchar(200) DEFAULT NULL COMMENT '商品规格',
  `quantity` int(11) NOT NULL COMMENT '数量',
  `unit_price` decimal(10,2) DEFAULT 0.00 COMMENT '单价',
  `amount` decimal(10,2) DEFAULT 0.00 COMMENT '小计',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_customer_order_id` (`customer_order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI分拣系统客户订单商品明细表' [ RunTime:0.012035s ]
[2025-07-31T10:10:19+08:00][sql] CREATE TABLE `fs_ai_inventory` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '库存ID',
  `member_id` int(11) NOT NULL COMMENT '会员ID',
  `product_name` varchar(200) NOT NULL COMMENT '商品名称',
  `product_sku` varchar(100) DEFAULT NULL COMMENT '商品SKU',
  `product_spec` varchar(200) DEFAULT NULL COMMENT '商品规格',
  `quantity` int(11) DEFAULT 0 COMMENT '库存数量',
  `unit_price` decimal(10,2) DEFAULT 0.00 COMMENT '单价',
  `total_value` decimal(10,2) DEFAULT 0.00 COMMENT '库存总价值',
  `location` varchar(100) DEFAULT NULL COMMENT '存放位置',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `member_product` (`member_id`, `product_sku`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_product_name` (`product_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI分拣系统库存表' [ RunTime:0.013952s ]
[2025-07-31T10:10:19+08:00][sql] CREATE TABLE `fs_ai_financial_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '财务记录ID',
  `member_id` int(11) NOT NULL COMMENT '会员ID',
  `type` varchar(20) NOT NULL COMMENT '类型:purchase_in,sale_out,refund',
  `amount` decimal(10,2) NOT NULL COMMENT '金额',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `related_order_id` int(11) DEFAULT NULL COMMENT '关联订单ID',
  `related_order_type` varchar(20) DEFAULT NULL COMMENT '关联订单类型:purchase,customer',
  `operator_id` int(11) NOT NULL COMMENT '操作人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_type` (`type`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_related_order` (`related_order_id`, `related_order_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI分拣系统财务记录表' [ RunTime:0.017387s ]
[2025-07-31T10:10:19+08:00][sql] ALTER TABLE `fs_ai_member_modules` ADD CONSTRAINT `fk_member_modules_member` FOREIGN KEY (`member_id`) REFERENCES `fs_ai_members` (`id`) ON DELETE CASCADE [ RunTime:0.029814s ]
[2025-07-31T10:10:19+08:00][sql] ALTER TABLE `fs_ai_purchase_orders` ADD CONSTRAINT `fk_purchase_orders_member` FOREIGN KEY (`member_id`) REFERENCES `fs_ai_members` (`id`) ON DELETE CASCADE [ RunTime:0.032450s ]
[2025-07-31T10:10:19+08:00][sql] ALTER TABLE `fs_ai_purchase_order_items` ADD CONSTRAINT `fk_purchase_items_order` FOREIGN KEY (`purchase_order_id`) REFERENCES `fs_ai_purchase_orders` (`id`) ON DELETE CASCADE [ RunTime:0.028143s ]
[2025-07-31T10:10:19+08:00][sql] ALTER TABLE `fs_ai_customer_orders` ADD CONSTRAINT `fk_customer_orders_member` FOREIGN KEY (`member_id`) REFERENCES `fs_ai_members` (`id`) ON DELETE CASCADE [ RunTime:0.037197s ]
[2025-07-31T10:10:19+08:00][sql] ALTER TABLE `fs_ai_customer_order_items` ADD CONSTRAINT `fk_customer_items_order` FOREIGN KEY (`customer_order_id`) REFERENCES `fs_ai_customer_orders` (`id`) ON DELETE CASCADE [ RunTime:0.032397s ]
[2025-07-31T10:10:19+08:00][sql] ALTER TABLE `fs_ai_inventory` ADD CONSTRAINT `fk_inventory_member` FOREIGN KEY (`member_id`) REFERENCES `fs_ai_members` (`id`) ON DELETE CASCADE [ RunTime:0.032920s ]
[2025-07-31T10:10:19+08:00][sql] ALTER TABLE `fs_ai_financial_records` ADD CONSTRAINT `fk_financial_member` FOREIGN KEY (`member_id`) REFERENCES `fs_ai_members` (`id`) ON DELETE CASCADE [ RunTime:0.030964s ]
[2025-07-31T10:10:19+08:00][sql] INSERT INTO `fs_ai_members` (`username`, `password`, `nickname`, `phone`, `email`, `parent_id`, `level`, `hierarchy_path`, `status`, `created_by`) VALUES
('ai_admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'AI分拣管理员', '13800138000', '<EMAIL>', 0, 1, '0', 1, 1) [ RunTime:0.002632s ]
[2025-07-31T10:10:19+08:00][sql] INSERT INTO `fs_ai_member_modules` (`member_id`, `module_name`, `module_title`, `is_enabled`) VALUES
(1, 'storage', '入库管理', 1),
(1, 'sorting', '分拣管理', 1),
(1, 'order_entry', '录单管理', 1),
(1, 'finance', '财务管理', 1) [ RunTime:0.003552s ]
[2025-07-31T10:10:19+08:00][sql] SHOW TABLES LIKE 'fs_ai_members' [ RunTime:0.001499s ]
[2025-07-31T10:10:19+08:00][sql] SHOW TABLES LIKE 'fs_ai_member_modules' [ RunTime:0.000784s ]
[2025-07-31T10:10:19+08:00][sql] SHOW TABLES LIKE 'fs_ai_purchase_orders' [ RunTime:0.000810s ]
[2025-07-31T10:10:19+08:00][sql] SHOW TABLES LIKE 'fs_ai_purchase_order_items' [ RunTime:0.000729s ]
[2025-07-31T10:10:19+08:00][sql] SHOW TABLES LIKE 'fs_ai_customer_orders' [ RunTime:0.000760s ]
[2025-07-31T10:10:19+08:00][sql] SHOW TABLES LIKE 'fs_ai_customer_order_items' [ RunTime:0.000794s ]
[2025-07-31T10:10:19+08:00][sql] SHOW TABLES LIKE 'fs_ai_inventory' [ RunTime:0.000746s ]
[2025-07-31T10:10:19+08:00][sql] SHOW TABLES LIKE 'fs_ai_financial_records' [ RunTime:0.000749s ]
[2025-07-31T10:10:19+08:00][sql] SHOW FULL COLUMNS FROM `fs_ai_members` [ RunTime:0.000983s ]
[2025-07-31T10:10:19+08:00][sql] SELECT * FROM `fs_ai_members` WHERE  `username` = 'ai_admin' LIMIT 1 [ RunTime:0.000534s ]
[2025-07-31T10:10:19+08:00][sql] SHOW FULL COLUMNS FROM `fs_ai_member_modules` [ RunTime:0.000854s ]
[2025-07-31T10:10:19+08:00][sql] SELECT * FROM `fs_ai_member_modules` WHERE  `member_id` = 1 [ RunTime:0.000482s ]
[2025-07-31T10:10:19+08:00][sql] SHOW FULL COLUMNS FROM `fs_admin` [ RunTime:0.001909s ]
[2025-07-31T10:10:19+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_admin` [ RunTime:0.000428s ]
[2025-07-31T10:10:19+08:00][sql] SHOW FULL COLUMNS FROM `fs_user` [ RunTime:0.002142s ]
[2025-07-31T10:10:19+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_user` [ RunTime:0.000416s ]
[2025-07-31T10:10:19+08:00][sql] SHOW FULL COLUMNS FROM `fs_role` [ RunTime:0.001861s ]
[2025-07-31T10:10:19+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_role` [ RunTime:0.000389s ]
[2025-07-31T10:17:32+08:00][sql] CONNECT:[ UseTime:0.011972s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-31T10:17:32+08:00][sql] DROP TABLE IF EXISTS `fs_ai_financial_records` [ RunTime:0.007652s ]
[2025-07-31T10:17:32+08:00][sql] DROP TABLE IF EXISTS `fs_ai_inventory` [ RunTime:0.006980s ]
[2025-07-31T10:17:32+08:00][sql] DROP TABLE IF EXISTS `fs_ai_customer_order_items` [ RunTime:0.006931s ]
[2025-07-31T10:17:32+08:00][sql] DROP TABLE IF EXISTS `fs_ai_customer_orders` [ RunTime:0.005557s ]
[2025-07-31T10:17:32+08:00][sql] DROP TABLE IF EXISTS `fs_ai_purchase_order_items` [ RunTime:0.005154s ]
[2025-07-31T10:17:32+08:00][sql] DROP TABLE IF EXISTS `fs_ai_purchase_orders` [ RunTime:0.005686s ]
[2025-07-31T10:17:32+08:00][sql] DROP TABLE IF EXISTS `fs_ai_member_modules` [ RunTime:0.005288s ]
[2025-07-31T10:17:32+08:00][sql] DROP TABLE IF EXISTS `fs_ai_members` [ RunTime:0.005705s ]
[2025-07-31T10:17:32+08:00][sql] DROP TABLE IF EXISTS `fs_members` [ RunTime:0.005415s ]
[2025-07-31T10:17:32+08:00][sql] DROP TABLE IF EXISTS `fs_member_modules` [ RunTime:0.000612s ]
[2025-07-31T10:17:32+08:00][sql] DROP TABLE IF EXISTS `fs_purchase_orders` [ RunTime:0.000560s ]
[2025-07-31T10:17:32+08:00][sql] DROP TABLE IF EXISTS `fs_purchase_order_items` [ RunTime:0.000536s ]
[2025-07-31T10:17:32+08:00][sql] DROP TABLE IF EXISTS `fs_customer_orders` [ RunTime:0.000585s ]
[2025-07-31T10:17:32+08:00][sql] DROP TABLE IF EXISTS `fs_customer_order_items` [ RunTime:0.000588s ]
[2025-07-31T10:17:32+08:00][sql] DROP TABLE IF EXISTS `fs_inventory` [ RunTime:0.000521s ]
[2025-07-31T10:17:32+08:00][sql] DROP TABLE IF EXISTS `fs_financial_records` [ RunTime:0.000503s ]
[2025-07-31T10:17:32+08:00][sql] ALTER TABLE `fs_user` 
ADD COLUMN `parent_id` int(11) DEFAULT 0 COMMENT '上级会员ID' AFTER `status`,
ADD COLUMN `level` int(11) DEFAULT 1 COMMENT '会员等级' AFTER `parent_id`,
ADD COLUMN `hierarchy_path` varchar(500) DEFAULT '0' COMMENT '层级路径' AFTER `level`,
ADD COLUMN `expire_time` datetime DEFAULT NULL COMMENT '到期时间' AFTER `hierarchy_path`,
ADD COLUMN `is_ai_member` tinyint(1) DEFAULT 0 COMMENT '是否AI分拣会员:1是,0否' AFTER `expire_time` [ RunTime:0.064911s ]
[2025-07-31T10:17:32+08:00][sql] CREATE TABLE `fs_user_modules` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `module_name` varchar(50) NOT NULL COMMENT '模块名称',
  `module_title` varchar(100) NOT NULL COMMENT '模块标题',
  `is_enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用:1启用,0禁用',
  `expire_time` datetime DEFAULT NULL COMMENT '模块到期时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_module` (`user_id`, `module_name`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_module_name` (`module_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户模块权限表' [ RunTime:0.016448s ]
[2025-07-31T10:17:32+08:00][sql] CREATE TABLE `fs_purchase_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '进货单ID',
  `order_no` varchar(50) NOT NULL COMMENT '进货单号',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `supplier_name` varchar(100) NOT NULL COMMENT '供应商名称',
  `supplier_contact` varchar(50) DEFAULT NULL COMMENT '供应商联系人',
  `supplier_phone` varchar(20) DEFAULT NULL COMMENT '供应商电话',
  `total_amount` decimal(10,2) DEFAULT 0.00 COMMENT '总金额',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态:1待入库,2已入库,3已取消',
  `remark` text COMMENT '备注',
  `created_by` int(11) NOT NULL COMMENT '创建人ID(fs_admin.id)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='进货单表' [ RunTime:0.018370s ]
[2025-07-31T10:17:32+08:00][sql] CREATE TABLE `fs_purchase_order_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `purchase_order_id` int(11) NOT NULL COMMENT '进货单ID',
  `product_name` varchar(200) NOT NULL COMMENT '商品名称',
  `product_sku` varchar(100) DEFAULT NULL COMMENT '商品SKU',
  `product_spec` varchar(200) DEFAULT NULL COMMENT '商品规格',
  `quantity` int(11) NOT NULL COMMENT '数量',
  `unit_price` decimal(10,2) DEFAULT 0.00 COMMENT '单价',
  `amount` decimal(10,2) DEFAULT 0.00 COMMENT '小计',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_purchase_order_id` (`purchase_order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='进货单商品明细表' [ RunTime:0.013336s ]
[2025-07-31T10:17:32+08:00][sql] CREATE TABLE `fs_customer_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(50) NOT NULL COMMENT '订单号',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `customer_name` varchar(100) NOT NULL COMMENT '客户姓名',
  `customer_phone` varchar(20) NOT NULL COMMENT '客户电话',
  `customer_address` varchar(500) DEFAULT NULL COMMENT '客户地址',
  `total_amount` decimal(10,2) DEFAULT 0.00 COMMENT '订单总金额',
  `selling_price` decimal(10,2) DEFAULT 0.00 COMMENT '销售价格',
  `cost_price` decimal(10,2) DEFAULT 0.00 COMMENT '成本价格',
  `profit` decimal(10,2) DEFAULT 0.00 COMMENT '利润',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态:1待定价,2待分配,3分拣中,4已完成,5已取消',
  `assigned_to` int(11) DEFAULT NULL COMMENT '分配给的分拣员ID(fs_user.id)',
  `assigned_at` datetime DEFAULT NULL COMMENT '分配时间',
  `remark` text COMMENT '备注',
  `created_by` int(11) NOT NULL COMMENT '创建人ID(fs_admin.id)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_assigned_to` (`assigned_to`),
  KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户订单表' [ RunTime:0.017200s ]
[2025-07-31T10:17:32+08:00][sql] CREATE TABLE `fs_customer_order_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `customer_order_id` int(11) NOT NULL COMMENT '客户订单ID',
  `product_name` varchar(200) NOT NULL COMMENT '商品名称',
  `product_sku` varchar(100) DEFAULT NULL COMMENT '商品SKU',
  `product_spec` varchar(200) DEFAULT NULL COMMENT '商品规格',
  `quantity` int(11) NOT NULL COMMENT '数量',
  `unit_price` decimal(10,2) DEFAULT 0.00 COMMENT '单价',
  `amount` decimal(10,2) DEFAULT 0.00 COMMENT '小计',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_customer_order_id` (`customer_order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户订单商品明细表' [ RunTime:0.012558s ]
[2025-07-31T10:17:32+08:00][sql] CREATE TABLE `fs_inventory` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '库存ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `product_name` varchar(200) NOT NULL COMMENT '商品名称',
  `product_sku` varchar(100) DEFAULT NULL COMMENT '商品SKU',
  `product_spec` varchar(200) DEFAULT NULL COMMENT '商品规格',
  `quantity` int(11) DEFAULT 0 COMMENT '库存数量',
  `unit_price` decimal(10,2) DEFAULT 0.00 COMMENT '单价',
  `total_value` decimal(10,2) DEFAULT 0.00 COMMENT '库存总价值',
  `location` varchar(100) DEFAULT NULL COMMENT '存放位置',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_product` (`user_id`, `product_sku`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_product_name` (`product_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存表' [ RunTime:0.014527s ]
[2025-07-31T10:17:32+08:00][sql] CREATE TABLE `fs_financial_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '财务记录ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `type` varchar(20) NOT NULL COMMENT '类型:purchase_in,sale_out,refund',
  `amount` decimal(10,2) NOT NULL COMMENT '金额',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `related_order_id` int(11) DEFAULT NULL COMMENT '关联订单ID',
  `related_order_type` varchar(20) DEFAULT NULL COMMENT '关联订单类型:purchase,customer',
  `operator_id` int(11) NOT NULL COMMENT '操作人ID(fs_admin.id)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_related_order` (`related_order_id`, `related_order_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='财务记录表' [ RunTime:0.016233s ]
[2025-07-31T10:17:33+08:00][sql] ALTER TABLE `fs_user_modules` ADD CONSTRAINT `fk_user_modules_user` FOREIGN KEY (`user_id`) REFERENCES `fs_user` (`id`) ON DELETE CASCADE [ RunTime:0.035525s ]
[2025-07-31T10:17:33+08:00][sql] ALTER TABLE `fs_purchase_orders` ADD CONSTRAINT `fk_purchase_orders_user` FOREIGN KEY (`user_id`) REFERENCES `fs_user` (`id`) ON DELETE CASCADE [ RunTime:0.033159s ]
[2025-07-31T10:17:33+08:00][sql] ALTER TABLE `fs_purchase_order_items` ADD CONSTRAINT `fk_purchase_items_order` FOREIGN KEY (`purchase_order_id`) REFERENCES `fs_purchase_orders` (`id`) ON DELETE CASCADE [ RunTime:0.028034s ]
[2025-07-31T10:17:33+08:00][sql] ALTER TABLE `fs_customer_orders` ADD CONSTRAINT `fk_customer_orders_user` FOREIGN KEY (`user_id`) REFERENCES `fs_user` (`id`) ON DELETE CASCADE [ RunTime:0.033713s ]
[2025-07-31T10:17:33+08:00][sql] ALTER TABLE `fs_customer_order_items` ADD CONSTRAINT `fk_customer_items_order` FOREIGN KEY (`customer_order_id`) REFERENCES `fs_customer_orders` (`id`) ON DELETE CASCADE [ RunTime:0.032607s ]
[2025-07-31T10:17:33+08:00][sql] ALTER TABLE `fs_inventory` ADD CONSTRAINT `fk_inventory_user` FOREIGN KEY (`user_id`) REFERENCES `fs_user` (`id`) ON DELETE CASCADE [ RunTime:0.034061s ]
[2025-07-31T10:17:33+08:00][sql] ALTER TABLE `fs_financial_records` ADD CONSTRAINT `fk_financial_user` FOREIGN KEY (`user_id`) REFERENCES `fs_user` (`id`) ON DELETE CASCADE [ RunTime:0.036757s ]
[2025-07-31T10:17:33+08:00][sql] INSERT INTO `fs_user` (`username`, `password`, `nickname`, `phone`, `email`, `parent_id`, `level`, `hierarchy_path`, `status`, `is_ai_member`, `created_at`) VALUES
('ai_test_user', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'AI分拣测试用户', '13800138001', '<EMAIL>', 0, 1, '0', 1, 1, NOW()) [ RunTime:0.001546s ]
[2025-07-31T10:17:33+08:00][sql] INSERT INTO `fs_user_modules` (`user_id`, `module_name`, `module_title`, `is_enabled`) VALUES
((SELECT id FROM fs_user WHERE username = 'ai_test_user'), 'storage', '入库管理', 1),
((SELECT id FROM fs_user WHERE username = 'ai_test_user'), 'sorting', '分拣管理', 1),
((SELECT id FROM fs_user WHERE username = 'ai_test_user'), 'order_entry', '录单管理', 1),
((SELECT id FROM fs_user WHERE username = 'ai_test_user'), 'finance', '财务管理', 1) [ RunTime:0.007157s ]
[2025-07-31T10:17:33+08:00][sql] SHOW TABLES LIKE 'fs_user_modules' [ RunTime:0.000867s ]
[2025-07-31T10:17:33+08:00][sql] SHOW TABLES LIKE 'fs_purchase_orders' [ RunTime:0.000819s ]
[2025-07-31T10:17:33+08:00][sql] SHOW TABLES LIKE 'fs_purchase_order_items' [ RunTime:0.000867s ]
[2025-07-31T10:17:33+08:00][sql] SHOW TABLES LIKE 'fs_customer_orders' [ RunTime:0.000848s ]
[2025-07-31T10:17:33+08:00][sql] SHOW TABLES LIKE 'fs_customer_order_items' [ RunTime:0.000897s ]
[2025-07-31T10:17:33+08:00][sql] SHOW TABLES LIKE 'fs_inventory' [ RunTime:0.000811s ]
[2025-07-31T10:17:33+08:00][sql] SHOW TABLES LIKE 'fs_financial_records' [ RunTime:0.000877s ]
[2025-07-31T10:17:33+08:00][sql] SHOW COLUMNS FROM fs_user [ RunTime:0.001000s ]
[2025-07-31T10:17:33+08:00][sql] SHOW FULL COLUMNS FROM `fs_user` [ RunTime:0.001059s ]
[2025-07-31T10:17:33+08:00][sql] SELECT * FROM `fs_user` WHERE  `username` = 'ai_test_user' LIMIT 1 [ RunTime:0.000558s ]
[2025-07-31T10:17:33+08:00][sql] SHOW FULL COLUMNS FROM `fs_user_modules` [ RunTime:0.000871s ]
[2025-07-31T10:17:33+08:00][sql] SELECT * FROM `fs_user_modules` WHERE  `user_id` = 8 [ RunTime:0.000665s ]
[2025-07-31T10:17:33+08:00][sql] SHOW FULL COLUMNS FROM `fs_admin` [ RunTime:0.002250s ]
[2025-07-31T10:17:33+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_admin` [ RunTime:0.000456s ]
[2025-07-31T10:17:33+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_user` [ RunTime:0.000670s ]
[2025-07-31T10:17:33+08:00][sql] SHOW FULL COLUMNS FROM `fs_role` [ RunTime:0.002098s ]
[2025-07-31T10:17:33+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_role` [ RunTime:0.000684s ]
[2025-07-31T10:19:01+08:00][sql] CONNECT:[ UseTime:0.002981s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-31T10:19:01+08:00][sql] SELECT DATABASE() as db_name [ RunTime:0.000701s ]
[2025-07-31T10:19:01+08:00][sql] SHOW DATABASES [ RunTime:0.001169s ]
[2025-07-31T10:19:50+08:00][sql] CONNECT:[ UseTime:0.002184s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-31T10:19:50+08:00][sql] SELECT DATABASE() as db_name [ RunTime:0.000367s ]
[2025-07-31T10:19:50+08:00][sql] USE aifen [ RunTime:0.000459s ]
[2025-07-31T10:19:50+08:00][sql] SELECT DATABASE() as db_name [ RunTime:0.000242s ]
[2025-07-31T10:19:50+08:00][sql] SHOW TABLES [ RunTime:0.001048s ]
[2025-07-31T10:20:02+08:00][sql] CONNECT:[ UseTime:0.002497s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-31T10:20:02+08:00][sql] DROP TABLE IF EXISTS `fs_ai_financial_records` [ RunTime:0.000848s ]
[2025-07-31T10:20:02+08:00][sql] DROP TABLE IF EXISTS `fs_ai_inventory` [ RunTime:0.000838s ]
[2025-07-31T10:20:02+08:00][sql] DROP TABLE IF EXISTS `fs_ai_customer_order_items` [ RunTime:0.000768s ]
[2025-07-31T10:20:02+08:00][sql] DROP TABLE IF EXISTS `fs_ai_customer_orders` [ RunTime:0.000856s ]
[2025-07-31T10:20:02+08:00][sql] DROP TABLE IF EXISTS `fs_ai_purchase_order_items` [ RunTime:0.000794s ]
[2025-07-31T10:20:02+08:00][sql] DROP TABLE IF EXISTS `fs_ai_purchase_orders` [ RunTime:0.000844s ]
[2025-07-31T10:20:02+08:00][sql] DROP TABLE IF EXISTS `fs_ai_member_modules` [ RunTime:0.000774s ]
[2025-07-31T10:20:02+08:00][sql] DROP TABLE IF EXISTS `fs_ai_members` [ RunTime:0.000926s ]
[2025-07-31T10:20:02+08:00][sql] DROP TABLE IF EXISTS `fs_members` [ RunTime:0.000959s ]
[2025-07-31T10:20:02+08:00][sql] DROP TABLE IF EXISTS `fs_member_modules` [ RunTime:0.000804s ]
[2025-07-31T10:20:02+08:00][sql] DROP TABLE IF EXISTS `fs_purchase_order_items` [ RunTime:0.007185s ]
[2025-07-31T10:20:02+08:00][sql] DROP TABLE IF EXISTS `fs_customer_order_items` [ RunTime:0.006195s ]
[2025-07-31T10:20:02+08:00][sql] DROP TABLE IF EXISTS `fs_inventory` [ RunTime:0.005415s ]
[2025-07-31T10:20:02+08:00][sql] DROP TABLE IF EXISTS `fs_financial_records` [ RunTime:0.005279s ]
[2025-07-31T10:20:02+08:00][sql] CREATE TABLE `fs_purchase_order_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `purchase_order_id` int(11) NOT NULL COMMENT '进货单ID',
  `product_name` varchar(200) NOT NULL COMMENT '商品名称',
  `product_sku` varchar(100) DEFAULT NULL COMMENT '商品SKU',
  `product_spec` varchar(200) DEFAULT NULL COMMENT '商品规格',
  `quantity` int(11) NOT NULL COMMENT '数量',
  `unit_price` decimal(10,2) DEFAULT 0.00 COMMENT '单价',
  `amount` decimal(10,2) DEFAULT 0.00 COMMENT '小计',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_purchase_order_id` (`purchase_order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='进货单商品明细表' [ RunTime:0.012952s ]
[2025-07-31T10:20:02+08:00][sql] CREATE TABLE `fs_customer_order_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `customer_order_id` int(11) NOT NULL COMMENT '客户订单ID',
  `product_name` varchar(200) NOT NULL COMMENT '商品名称',
  `product_sku` varchar(100) DEFAULT NULL COMMENT '商品SKU',
  `product_spec` varchar(200) DEFAULT NULL COMMENT '商品规格',
  `quantity` int(11) NOT NULL COMMENT '数量',
  `unit_price` decimal(10,2) DEFAULT 0.00 COMMENT '单价',
  `amount` decimal(10,2) DEFAULT 0.00 COMMENT '小计',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_customer_order_id` (`customer_order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户订单商品明细表' [ RunTime:0.012751s ]
[2025-07-31T10:20:02+08:00][sql] CREATE TABLE `fs_inventory` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '库存ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `product_name` varchar(200) NOT NULL COMMENT '商品名称',
  `product_sku` varchar(100) DEFAULT NULL COMMENT '商品SKU',
  `product_spec` varchar(200) DEFAULT NULL COMMENT '商品规格',
  `quantity` int(11) DEFAULT 0 COMMENT '库存数量',
  `unit_price` decimal(10,2) DEFAULT 0.00 COMMENT '单价',
  `total_value` decimal(10,2) DEFAULT 0.00 COMMENT '库存总价值',
  `location` varchar(100) DEFAULT NULL COMMENT '存放位置',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_product` (`user_id`, `product_sku`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_product_name` (`product_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存表' [ RunTime:0.015259s ]
[2025-07-31T10:20:02+08:00][sql] CREATE TABLE `fs_financial_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '财务记录ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `type` varchar(20) NOT NULL COMMENT '类型:purchase_in,sale_out,refund',
  `amount` decimal(10,2) NOT NULL COMMENT '金额',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `related_order_id` int(11) DEFAULT NULL COMMENT '关联订单ID',
  `related_order_type` varchar(20) DEFAULT NULL COMMENT '关联订单类型:purchase,customer',
  `operator_id` int(11) NOT NULL COMMENT '操作人ID(fs_admin.id)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_related_order` (`related_order_id`, `related_order_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='财务记录表' [ RunTime:0.015973s ]
[2025-07-31T10:20:02+08:00][sql] ALTER TABLE `fs_purchase_order_items` ADD CONSTRAINT `fk_purchase_items_order` FOREIGN KEY (`purchase_order_id`) REFERENCES `fs_purchase_orders` (`id`) ON DELETE CASCADE [ RunTime:0.031245s ]
[2025-07-31T10:20:02+08:00][sql] ALTER TABLE `fs_customer_order_items` ADD CONSTRAINT `fk_customer_items_order` FOREIGN KEY (`customer_order_id`) REFERENCES `fs_customer_orders` (`id`) ON DELETE CASCADE [ RunTime:0.028597s ]
[2025-07-31T10:20:02+08:00][sql] ALTER TABLE `fs_inventory` ADD CONSTRAINT `fk_inventory_user` FOREIGN KEY (`user_id`) REFERENCES `fs_user` (`id`) ON DELETE CASCADE [ RunTime:0.033219s ]
[2025-07-31T10:20:02+08:00][sql] ALTER TABLE `fs_financial_records` ADD CONSTRAINT `fk_financial_user` FOREIGN KEY (`user_id`) REFERENCES `fs_user` (`id`) ON DELETE CASCADE [ RunTime:0.031979s ]
[2025-07-31T10:20:02+08:00][sql] SHOW TABLES LIKE 'fs_user_modules' [ RunTime:0.000930s ]
[2025-07-31T10:20:02+08:00][sql] SHOW TABLES LIKE 'fs_purchase_orders' [ RunTime:0.000819s ]
[2025-07-31T10:20:02+08:00][sql] SHOW TABLES LIKE 'fs_purchase_order_items' [ RunTime:0.000798s ]
[2025-07-31T10:20:02+08:00][sql] SHOW TABLES LIKE 'fs_customer_orders' [ RunTime:0.000803s ]
[2025-07-31T10:20:02+08:00][sql] SHOW TABLES LIKE 'fs_customer_order_items' [ RunTime:0.000825s ]
[2025-07-31T10:20:02+08:00][sql] SHOW TABLES LIKE 'fs_inventory' [ RunTime:0.000807s ]
[2025-07-31T10:20:02+08:00][sql] SHOW TABLES LIKE 'fs_financial_records' [ RunTime:0.000785s ]
[2025-07-31T10:20:02+08:00][sql] SHOW COLUMNS FROM fs_user [ RunTime:0.000937s ]
[2025-07-31T10:20:02+08:00][sql] SHOW FULL COLUMNS FROM `fs_user` [ RunTime:0.001097s ]
[2025-07-31T10:20:02+08:00][sql] SELECT * FROM `fs_user` WHERE  `username` = 'ai_test_user' LIMIT 1 [ RunTime:0.000718s ]
[2025-07-31T10:20:02+08:00][sql] SHOW FULL COLUMNS FROM `fs_user_modules` [ RunTime:0.000873s ]
[2025-07-31T10:20:02+08:00][sql] SELECT * FROM `fs_user_modules` WHERE  `user_id` = 8 [ RunTime:0.000528s ]
[2025-07-31T10:20:02+08:00][sql] SHOW FULL COLUMNS FROM `fs_admin` [ RunTime:0.002306s ]
[2025-07-31T10:20:02+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_admin` [ RunTime:0.000462s ]
[2025-07-31T10:20:02+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_user` [ RunTime:0.000431s ]
[2025-07-31T10:20:02+08:00][sql] SHOW FULL COLUMNS FROM `fs_role` [ RunTime:0.002166s ]
[2025-07-31T10:20:02+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_role` [ RunTime:0.000423s ]
[2025-07-31T10:21:57+08:00][sql] CONNECT:[ UseTime:0.002362s ] mysql:host=127.0.0.1;port=3306;dbname=fanshop;charset=utf8mb4
[2025-07-31T10:21:57+08:00][sql] SELECT DATABASE() as db_name [ RunTime:0.000393s ]
[2025-07-31T10:21:57+08:00][sql] SHOW TABLES [ RunTime:0.000724s ]
[2025-07-31T10:21:57+08:00][sql] SHOW TABLES LIKE 'fs_user_modules' [ RunTime:0.000685s ]
[2025-07-31T10:21:57+08:00][sql] SHOW TABLES LIKE 'fs_purchase_orders' [ RunTime:0.000679s ]
[2025-07-31T10:21:57+08:00][sql] SHOW TABLES LIKE 'fs_customer_orders' [ RunTime:0.000704s ]
[2025-07-31T10:21:57+08:00][sql] SHOW TABLES LIKE 'fs_inventory' [ RunTime:0.000639s ]
[2025-07-31T10:21:57+08:00][sql] SHOW TABLES LIKE 'fs_financial_records' [ RunTime:0.000681s ]
[2025-07-31T10:21:57+08:00][sql] SHOW COLUMNS FROM fs_user [ RunTime:0.002016s ]
[2025-07-31T10:21:57+08:00][sql] SHOW FULL COLUMNS FROM `fs_user` [ RunTime:0.000852s ]
[2025-07-31T10:21:57+08:00][sql] SELECT * FROM `fs_user` [ RunTime:0.000469s ]
[2025-07-31T10:23:02+08:00][sql] CONNECT:[ UseTime:0.002530s ] mysql:host=127.0.0.1;port=3306;dbname=aifen;charset=utf8mb4
[2025-07-31T10:23:02+08:00][sql] SELECT DATABASE() as db_name [ RunTime:0.000496s ]
[2025-07-31T10:23:02+08:00][sql] SHOW TABLES [ RunTime:0.001075s ]
[2025-07-31T10:23:02+08:00][sql] SHOW TABLES LIKE 'fs_user_modules' [ RunTime:0.001068s ]
[2025-07-31T10:23:02+08:00][sql] SHOW TABLES LIKE 'fs_purchase_orders' [ RunTime:0.001008s ]
[2025-07-31T10:23:02+08:00][sql] SHOW TABLES LIKE 'fs_customer_orders' [ RunTime:0.001076s ]
[2025-07-31T10:23:02+08:00][sql] SHOW TABLES LIKE 'fs_inventory' [ RunTime:0.001006s ]
[2025-07-31T10:23:02+08:00][sql] SHOW TABLES LIKE 'fs_financial_records' [ RunTime:0.001040s ]
[2025-07-31T10:23:02+08:00][sql] SHOW COLUMNS FROM fs_user [ RunTime:0.001634s ]
[2025-07-31T10:23:02+08:00][sql] SHOW FULL COLUMNS FROM `fs_user` [ RunTime:0.001142s ]
[2025-07-31T10:23:02+08:00][sql] SELECT * FROM `fs_user` [ RunTime:0.000557s ]
[2025-07-31T10:23:28+08:00][sql] CONNECT:[ UseTime:0.015814s ] mysql:host=127.0.0.1;port=3306;dbname=aifen;charset=utf8mb4
[2025-07-31T10:23:28+08:00][sql] DROP TABLE IF EXISTS `fs_ai_financial_records` [ RunTime:0.000539s ]
[2025-07-31T10:23:28+08:00][sql] DROP TABLE IF EXISTS `fs_ai_inventory` [ RunTime:0.000469s ]
[2025-07-31T10:23:28+08:00][sql] DROP TABLE IF EXISTS `fs_ai_customer_order_items` [ RunTime:0.000622s ]
[2025-07-31T10:23:28+08:00][sql] DROP TABLE IF EXISTS `fs_ai_customer_orders` [ RunTime:0.000605s ]
[2025-07-31T10:23:28+08:00][sql] DROP TABLE IF EXISTS `fs_ai_purchase_order_items` [ RunTime:0.000680s ]
[2025-07-31T10:23:28+08:00][sql] DROP TABLE IF EXISTS `fs_ai_purchase_orders` [ RunTime:0.000442s ]
[2025-07-31T10:23:28+08:00][sql] DROP TABLE IF EXISTS `fs_ai_member_modules` [ RunTime:0.000496s ]
[2025-07-31T10:23:28+08:00][sql] DROP TABLE IF EXISTS `fs_ai_members` [ RunTime:0.000469s ]
[2025-07-31T10:23:28+08:00][sql] DROP TABLE IF EXISTS `fs_members` [ RunTime:0.000528s ]
[2025-07-31T10:23:28+08:00][sql] DROP TABLE IF EXISTS `fs_member_modules` [ RunTime:0.000497s ]
[2025-07-31T10:23:28+08:00][sql] DROP TABLE IF EXISTS `fs_purchase_orders` [ RunTime:0.000467s ]
[2025-07-31T10:23:28+08:00][sql] DROP TABLE IF EXISTS `fs_purchase_order_items` [ RunTime:0.000525s ]
[2025-07-31T10:23:28+08:00][sql] DROP TABLE IF EXISTS `fs_customer_orders` [ RunTime:0.000425s ]
[2025-07-31T10:23:28+08:00][sql] DROP TABLE IF EXISTS `fs_customer_order_items` [ RunTime:0.000432s ]
[2025-07-31T10:23:28+08:00][sql] DROP TABLE IF EXISTS `fs_inventory` [ RunTime:0.000833s ]
[2025-07-31T10:23:28+08:00][sql] DROP TABLE IF EXISTS `fs_financial_records` [ RunTime:0.000810s ]
[2025-07-31T10:23:28+08:00][sql] ALTER TABLE `fs_user` 
ADD COLUMN `parent_id` int(11) DEFAULT 0 COMMENT '上级会员ID' AFTER `status`,
ADD COLUMN `level` int(11) DEFAULT 1 COMMENT '会员等级' AFTER `parent_id`,
ADD COLUMN `hierarchy_path` varchar(500) DEFAULT '0' COMMENT '层级路径' AFTER `level`,
ADD COLUMN `expire_time` datetime DEFAULT NULL COMMENT '到期时间' AFTER `hierarchy_path`,
ADD COLUMN `is_ai_member` tinyint(1) DEFAULT 0 COMMENT '是否AI分拣会员:1是,0否' AFTER `expire_time` [ RunTime:0.066782s ]
[2025-07-31T10:23:28+08:00][sql] CREATE TABLE `fs_user_modules` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `module_name` varchar(50) NOT NULL COMMENT '模块名称',
  `module_title` varchar(100) NOT NULL COMMENT '模块标题',
  `is_enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用:1启用,0禁用',
  `expire_time` datetime DEFAULT NULL COMMENT '模块到期时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_module` (`user_id`, `module_name`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_module_name` (`module_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户模块权限表' [ RunTime:0.013741s ]
[2025-07-31T10:23:28+08:00][sql] CREATE TABLE `fs_purchase_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '进货单ID',
  `order_no` varchar(50) NOT NULL COMMENT '进货单号',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `supplier_name` varchar(100) NOT NULL COMMENT '供应商名称',
  `supplier_contact` varchar(50) DEFAULT NULL COMMENT '供应商联系人',
  `supplier_phone` varchar(20) DEFAULT NULL COMMENT '供应商电话',
  `total_amount` decimal(10,2) DEFAULT 0.00 COMMENT '总金额',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态:1待入库,2已入库,3已取消',
  `remark` text COMMENT '备注',
  `created_by` int(11) NOT NULL COMMENT '创建人ID(fs_admin.id)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='进货单表' [ RunTime:0.016344s ]
[2025-07-31T10:23:28+08:00][sql] CREATE TABLE `fs_purchase_order_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `purchase_order_id` int(11) NOT NULL COMMENT '进货单ID',
  `product_name` varchar(200) NOT NULL COMMENT '商品名称',
  `product_sku` varchar(100) DEFAULT NULL COMMENT '商品SKU',
  `product_spec` varchar(200) DEFAULT NULL COMMENT '商品规格',
  `quantity` int(11) NOT NULL COMMENT '数量',
  `unit_price` decimal(10,2) DEFAULT 0.00 COMMENT '单价',
  `amount` decimal(10,2) DEFAULT 0.00 COMMENT '小计',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_purchase_order_id` (`purchase_order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='进货单商品明细表' [ RunTime:0.014107s ]
[2025-07-31T10:23:28+08:00][sql] CREATE TABLE `fs_customer_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(50) NOT NULL COMMENT '订单号',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `customer_name` varchar(100) NOT NULL COMMENT '客户姓名',
  `customer_phone` varchar(20) NOT NULL COMMENT '客户电话',
  `customer_address` varchar(500) DEFAULT NULL COMMENT '客户地址',
  `total_amount` decimal(10,2) DEFAULT 0.00 COMMENT '订单总金额',
  `selling_price` decimal(10,2) DEFAULT 0.00 COMMENT '销售价格',
  `cost_price` decimal(10,2) DEFAULT 0.00 COMMENT '成本价格',
  `profit` decimal(10,2) DEFAULT 0.00 COMMENT '利润',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态:1待定价,2待分配,3分拣中,4已完成,5已取消',
  `assigned_to` int(11) DEFAULT NULL COMMENT '分配给的分拣员ID(fs_user.id)',
  `assigned_at` datetime DEFAULT NULL COMMENT '分配时间',
  `remark` text COMMENT '备注',
  `created_by` int(11) NOT NULL COMMENT '创建人ID(fs_admin.id)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_assigned_to` (`assigned_to`),
  KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户订单表' [ RunTime:0.018188s ]
[2025-07-31T10:23:28+08:00][sql] CREATE TABLE `fs_customer_order_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `customer_order_id` int(11) NOT NULL COMMENT '客户订单ID',
  `product_name` varchar(200) NOT NULL COMMENT '商品名称',
  `product_sku` varchar(100) DEFAULT NULL COMMENT '商品SKU',
  `product_spec` varchar(200) DEFAULT NULL COMMENT '商品规格',
  `quantity` int(11) NOT NULL COMMENT '数量',
  `unit_price` decimal(10,2) DEFAULT 0.00 COMMENT '单价',
  `amount` decimal(10,2) DEFAULT 0.00 COMMENT '小计',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_customer_order_id` (`customer_order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户订单商品明细表' [ RunTime:0.011496s ]
[2025-07-31T10:23:28+08:00][sql] CREATE TABLE `fs_inventory` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '库存ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `product_name` varchar(200) NOT NULL COMMENT '商品名称',
  `product_sku` varchar(100) DEFAULT NULL COMMENT '商品SKU',
  `product_spec` varchar(200) DEFAULT NULL COMMENT '商品规格',
  `quantity` int(11) DEFAULT 0 COMMENT '库存数量',
  `unit_price` decimal(10,2) DEFAULT 0.00 COMMENT '单价',
  `total_value` decimal(10,2) DEFAULT 0.00 COMMENT '库存总价值',
  `location` varchar(100) DEFAULT NULL COMMENT '存放位置',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_product` (`user_id`, `product_sku`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_product_name` (`product_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存表' [ RunTime:0.013806s ]
[2025-07-31T10:23:28+08:00][sql] CREATE TABLE `fs_financial_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '财务记录ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `type` varchar(20) NOT NULL COMMENT '类型:purchase_in,sale_out,refund',
  `amount` decimal(10,2) NOT NULL COMMENT '金额',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `related_order_id` int(11) DEFAULT NULL COMMENT '关联订单ID',
  `related_order_type` varchar(20) DEFAULT NULL COMMENT '关联订单类型:purchase,customer',
  `operator_id` int(11) NOT NULL COMMENT '操作人ID(fs_admin.id)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_related_order` (`related_order_id`, `related_order_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='财务记录表' [ RunTime:0.015185s ]
[2025-07-31T10:23:28+08:00][sql] ALTER TABLE `fs_user_modules` ADD CONSTRAINT `fk_user_modules_user` FOREIGN KEY (`user_id`) REFERENCES `fs_user` (`id`) ON DELETE CASCADE [ RunTime:0.029579s ]
[2025-07-31T10:23:28+08:00][sql] ALTER TABLE `fs_purchase_orders` ADD CONSTRAINT `fk_purchase_orders_user` FOREIGN KEY (`user_id`) REFERENCES `fs_user` (`id`) ON DELETE CASCADE [ RunTime:0.036151s ]
[2025-07-31T10:23:28+08:00][sql] ALTER TABLE `fs_purchase_order_items` ADD CONSTRAINT `fk_purchase_items_order` FOREIGN KEY (`purchase_order_id`) REFERENCES `fs_purchase_orders` (`id`) ON DELETE CASCADE [ RunTime:0.027126s ]
[2025-07-31T10:23:28+08:00][sql] ALTER TABLE `fs_customer_orders` ADD CONSTRAINT `fk_customer_orders_user` FOREIGN KEY (`user_id`) REFERENCES `fs_user` (`id`) ON DELETE CASCADE [ RunTime:0.032848s ]
[2025-07-31T10:23:28+08:00][sql] ALTER TABLE `fs_customer_order_items` ADD CONSTRAINT `fk_customer_items_order` FOREIGN KEY (`customer_order_id`) REFERENCES `fs_customer_orders` (`id`) ON DELETE CASCADE [ RunTime:0.028312s ]
[2025-07-31T10:23:28+08:00][sql] ALTER TABLE `fs_inventory` ADD CONSTRAINT `fk_inventory_user` FOREIGN KEY (`user_id`) REFERENCES `fs_user` (`id`) ON DELETE CASCADE [ RunTime:0.037330s ]
[2025-07-31T10:23:28+08:00][sql] ALTER TABLE `fs_financial_records` ADD CONSTRAINT `fk_financial_user` FOREIGN KEY (`user_id`) REFERENCES `fs_user` (`id`) ON DELETE CASCADE [ RunTime:0.041879s ]
[2025-07-31T10:23:28+08:00][sql] INSERT INTO `fs_user` (`username`, `password`, `nickname`, `phone`, `email`, `parent_id`, `level`, `hierarchy_path`, `status`, `is_ai_member`, `created_at`) VALUES
('ai_test_user', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'AI分拣测试用户', '13800138001', '<EMAIL>', 0, 1, '0', 1, 1, NOW()) [ RunTime:0.001373s ]
[2025-07-31T10:23:28+08:00][sql] INSERT INTO `fs_user_modules` (`user_id`, `module_name`, `module_title`, `is_enabled`) VALUES
((SELECT id FROM fs_user WHERE username = 'ai_test_user'), 'storage', '入库管理', 1),
((SELECT id FROM fs_user WHERE username = 'ai_test_user'), 'sorting', '分拣管理', 1),
((SELECT id FROM fs_user WHERE username = 'ai_test_user'), 'order_entry', '录单管理', 1),
((SELECT id FROM fs_user WHERE username = 'ai_test_user'), 'finance', '财务管理', 1) [ RunTime:0.006954s ]
[2025-07-31T10:23:28+08:00][sql] SHOW TABLES LIKE 'fs_user_modules' [ RunTime:0.000927s ]
[2025-07-31T10:23:28+08:00][sql] SHOW TABLES LIKE 'fs_purchase_orders' [ RunTime:0.000857s ]
[2025-07-31T10:23:28+08:00][sql] SHOW TABLES LIKE 'fs_purchase_order_items' [ RunTime:0.000735s ]
[2025-07-31T10:23:28+08:00][sql] SHOW TABLES LIKE 'fs_customer_orders' [ RunTime:0.000840s ]
[2025-07-31T10:23:28+08:00][sql] SHOW TABLES LIKE 'fs_customer_order_items' [ RunTime:0.000770s ]
[2025-07-31T10:23:28+08:00][sql] SHOW TABLES LIKE 'fs_inventory' [ RunTime:0.000749s ]
[2025-07-31T10:23:28+08:00][sql] SHOW TABLES LIKE 'fs_financial_records' [ RunTime:0.000890s ]
[2025-07-31T10:23:28+08:00][sql] SHOW COLUMNS FROM fs_user [ RunTime:0.001107s ]
[2025-07-31T10:23:28+08:00][sql] SHOW FULL COLUMNS FROM `fs_user` [ RunTime:0.000902s ]
[2025-07-31T10:23:28+08:00][sql] SELECT * FROM `fs_user` WHERE  `username` = 'ai_test_user' LIMIT 1 [ RunTime:0.000525s ]
[2025-07-31T10:23:28+08:00][sql] SHOW FULL COLUMNS FROM `fs_user_modules` [ RunTime:0.000799s ]
[2025-07-31T10:23:28+08:00][sql] SELECT * FROM `fs_user_modules` WHERE  `user_id` = 8 [ RunTime:0.000470s ]
[2025-07-31T10:23:28+08:00][sql] SHOW FULL COLUMNS FROM `fs_admin` [ RunTime:0.003637s ]
[2025-07-31T10:23:28+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_admin` [ RunTime:0.000599s ]
[2025-07-31T10:23:28+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_user` [ RunTime:0.000608s ]
[2025-07-31T10:23:28+08:00][sql] SHOW FULL COLUMNS FROM `fs_role` [ RunTime:0.001846s ]
[2025-07-31T10:23:28+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_role` [ RunTime:0.000436s ]
[2025-07-31T10:23:38+08:00][sql] CONNECT:[ UseTime:0.002547s ] mysql:host=127.0.0.1;port=3306;dbname=aifen;charset=utf8mb4
[2025-07-31T10:23:38+08:00][sql] SELECT DATABASE() as db_name [ RunTime:0.000528s ]
[2025-07-31T10:23:38+08:00][sql] SHOW TABLES [ RunTime:0.001621s ]
[2025-07-31T10:23:38+08:00][sql] SHOW TABLES LIKE 'fs_user_modules' [ RunTime:0.001407s ]
[2025-07-31T10:23:38+08:00][sql] SHOW TABLES LIKE 'fs_purchase_orders' [ RunTime:0.001401s ]
[2025-07-31T10:23:38+08:00][sql] SHOW TABLES LIKE 'fs_customer_orders' [ RunTime:0.001338s ]
[2025-07-31T10:23:38+08:00][sql] SHOW TABLES LIKE 'fs_inventory' [ RunTime:0.001410s ]
[2025-07-31T10:23:38+08:00][sql] SHOW TABLES LIKE 'fs_financial_records' [ RunTime:0.001312s ]
[2025-07-31T10:23:38+08:00][sql] SHOW COLUMNS FROM fs_user [ RunTime:0.003911s ]
[2025-07-31T10:23:38+08:00][sql] SHOW FULL COLUMNS FROM `fs_user` [ RunTime:0.001060s ]
[2025-07-31T10:23:38+08:00][sql] SELECT * FROM `fs_user` [ RunTime:0.000487s ]
[2025-07-31T10:37:45+08:00][sql] CONNECT:[ UseTime:0.004457s ] mysql:host=127.0.0.1;port=3306;dbname=aifen;charset=utf8mb4
[2025-07-31T10:37:46+08:00][sql] SHOW TABLES [ RunTime:0.001683s ]
[2025-07-31T10:37:46+08:00][sql] SHOW FULL COLUMNS FROM `fs_admin` [ RunTime:0.002573s ]
[2025-07-31T10:37:46+08:00][sql] SELECT * FROM `fs_admin` [ RunTime:0.000745s ]
[2025-07-31T10:37:46+08:00][sql] SHOW FULL COLUMNS FROM `fs_user` [ RunTime:0.001117s ]
[2025-07-31T10:37:46+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_user` [ RunTime:0.000466s ]
[2025-07-31T10:37:46+08:00][sql] SHOW FULL COLUMNS FROM `fs_role` [ RunTime:0.002042s ]
[2025-07-31T10:37:46+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_role` [ RunTime:0.000419s ]
[2025-07-31T10:37:46+08:00][sql] SHOW FULL COLUMNS FROM `fs_menu` [ RunTime:0.000928s ]
[2025-07-31T10:37:46+08:00][sql] SELECT COUNT(*) AS think_count FROM `fs_menu` [ RunTime:0.000452s ]
[2025-07-31T10:37:46+08:00][sql] SHOW TABLES LIKE 'fs_members' [ RunTime:0.001009s ]
[2025-07-31T10:37:46+08:00][sql] SHOW TABLES LIKE 'fs_member_modules' [ RunTime:0.001067s ]
[2025-07-31T10:37:46+08:00][sql] SHOW TABLES LIKE 'fs_purchase_orders' [ RunTime:0.000802s ]
[2025-07-31T10:37:46+08:00][sql] SHOW TABLES LIKE 'fs_purchase_order_items' [ RunTime:0.000857s ]
[2025-07-31T10:37:46+08:00][sql] SHOW TABLES LIKE 'fs_customer_orders' [ RunTime:0.000914s ]
[2025-07-31T10:37:46+08:00][sql] SHOW TABLES LIKE 'fs_customer_order_items' [ RunTime:0.000911s ]
[2025-07-31T10:37:46+08:00][sql] SHOW TABLES LIKE 'fs_inventory' [ RunTime:0.000904s ]
[2025-07-31T10:37:46+08:00][sql] SHOW TABLES LIKE 'fs_financial_records' [ RunTime:0.000910s ]
