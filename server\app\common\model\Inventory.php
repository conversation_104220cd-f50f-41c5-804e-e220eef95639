<?php
declare(strict_types=1);

namespace app\common\model;

use think\Model;

/**
 * 库存模型
 */
class Inventory extends Model
{
    // 表名
    protected $table = 'fs_inventory';

    // 主键
    protected $pk = 'id';

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // 字段类型转换
    protected $type = [
        'member_id' => 'integer',
        'current_stock' => 'integer',
        'total_in' => 'integer',
        'total_out' => 'integer',
        'last_purchase_price' => 'float',
        'average_cost' => 'float',
    ];

    // 关联会员
    public function member()
    {
        return $this->belongsTo(Member::class, 'member_id', 'id');
    }
}
