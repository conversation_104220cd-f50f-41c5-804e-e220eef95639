-- AI分拣系统扩展SQL
-- 基于现有的电商管理系统进行扩展

-- 1. 清理可能存在的冲突表
DROP TABLE IF EXISTS `fs_members`;
DROP TABLE IF EXISTS `fs_purchase_orders`;
DROP TABLE IF EXISTS `fs_customer_orders`;

-- 2. 创建AI分拣系统的会员表 (多级会员管理)
CREATE TABLE `fs_ai_members` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '会员ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `parent_id` int(11) DEFAULT 0 COMMENT '上级会员ID',
  `level` int(11) DEFAULT 1 COMMENT '会员等级',
  `hierarchy_path` varchar(500) DEFAULT '0' COMMENT '层级路径',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态:1启用,0禁用',
  `expire_time` datetime DEFAULT NULL COMMENT '到期时间',
  `created_by` int(11) DEFAULT NULL COMMENT '创建人ID(关联fs_admin.id)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI分拣系统会员表';

-- 3. 创建会员模块权限表
CREATE TABLE `fs_ai_member_modules` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `member_id` int(11) NOT NULL COMMENT '会员ID',
  `module_name` varchar(50) NOT NULL COMMENT '模块名称',
  `module_title` varchar(100) NOT NULL COMMENT '模块标题',
  `is_enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用:1启用,0禁用',
  `expire_time` datetime DEFAULT NULL COMMENT '模块到期时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `member_module` (`member_id`, `module_name`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_module_name` (`module_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI分拣系统会员模块权限表';

-- 4. 创建进货单表
CREATE TABLE `fs_ai_purchase_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '进货单ID',
  `order_no` varchar(50) NOT NULL COMMENT '进货单号',
  `member_id` int(11) NOT NULL COMMENT '会员ID',
  `supplier_name` varchar(100) NOT NULL COMMENT '供应商名称',
  `supplier_contact` varchar(50) DEFAULT NULL COMMENT '供应商联系人',
  `supplier_phone` varchar(20) DEFAULT NULL COMMENT '供应商电话',
  `total_amount` decimal(10,2) DEFAULT 0.00 COMMENT '总金额',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态:1待入库,2已入库,3已取消',
  `remark` text COMMENT '备注',
  `created_by` int(11) NOT NULL COMMENT '创建人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI分拣系统进货单表';

-- 5. 创建进货单商品明细表
CREATE TABLE `fs_ai_purchase_order_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `purchase_order_id` int(11) NOT NULL COMMENT '进货单ID',
  `product_name` varchar(200) NOT NULL COMMENT '商品名称',
  `product_sku` varchar(100) DEFAULT NULL COMMENT '商品SKU',
  `product_spec` varchar(200) DEFAULT NULL COMMENT '商品规格',
  `quantity` int(11) NOT NULL COMMENT '数量',
  `unit_price` decimal(10,2) DEFAULT 0.00 COMMENT '单价',
  `amount` decimal(10,2) DEFAULT 0.00 COMMENT '小计',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_purchase_order_id` (`purchase_order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI分拣系统进货单商品明细表';

-- 6. 创建客户订单表
CREATE TABLE `fs_ai_customer_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(50) NOT NULL COMMENT '订单号',
  `member_id` int(11) NOT NULL COMMENT '会员ID',
  `customer_name` varchar(100) NOT NULL COMMENT '客户姓名',
  `customer_phone` varchar(20) NOT NULL COMMENT '客户电话',
  `customer_address` varchar(500) DEFAULT NULL COMMENT '客户地址',
  `total_amount` decimal(10,2) DEFAULT 0.00 COMMENT '订单总金额',
  `selling_price` decimal(10,2) DEFAULT 0.00 COMMENT '销售价格',
  `cost_price` decimal(10,2) DEFAULT 0.00 COMMENT '成本价格',
  `profit` decimal(10,2) DEFAULT 0.00 COMMENT '利润',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态:1待定价,2待分配,3分拣中,4已完成,5已取消',
  `assigned_to` int(11) DEFAULT NULL COMMENT '分配给的分拣员ID',
  `assigned_at` datetime DEFAULT NULL COMMENT '分配时间',
  `remark` text COMMENT '备注',
  `created_by` int(11) NOT NULL COMMENT '创建人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_no` (`order_no`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_status` (`status`),
  KEY `idx_assigned_to` (`assigned_to`),
  KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI分拣系统客户订单表';

-- 7. 创建客户订单商品明细表
CREATE TABLE `fs_ai_customer_order_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `customer_order_id` int(11) NOT NULL COMMENT '客户订单ID',
  `product_name` varchar(200) NOT NULL COMMENT '商品名称',
  `product_sku` varchar(100) DEFAULT NULL COMMENT '商品SKU',
  `product_spec` varchar(200) DEFAULT NULL COMMENT '商品规格',
  `quantity` int(11) NOT NULL COMMENT '数量',
  `unit_price` decimal(10,2) DEFAULT 0.00 COMMENT '单价',
  `amount` decimal(10,2) DEFAULT 0.00 COMMENT '小计',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_customer_order_id` (`customer_order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI分拣系统客户订单商品明细表';

-- 8. 创建库存表
CREATE TABLE `fs_ai_inventory` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '库存ID',
  `member_id` int(11) NOT NULL COMMENT '会员ID',
  `product_name` varchar(200) NOT NULL COMMENT '商品名称',
  `product_sku` varchar(100) DEFAULT NULL COMMENT '商品SKU',
  `product_spec` varchar(200) DEFAULT NULL COMMENT '商品规格',
  `quantity` int(11) DEFAULT 0 COMMENT '库存数量',
  `unit_price` decimal(10,2) DEFAULT 0.00 COMMENT '单价',
  `total_value` decimal(10,2) DEFAULT 0.00 COMMENT '库存总价值',
  `location` varchar(100) DEFAULT NULL COMMENT '存放位置',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `member_product` (`member_id`, `product_sku`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_product_name` (`product_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI分拣系统库存表';

-- 9. 创建财务记录表
CREATE TABLE `fs_ai_financial_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '财务记录ID',
  `member_id` int(11) NOT NULL COMMENT '会员ID',
  `type` varchar(20) NOT NULL COMMENT '类型:purchase_in,sale_out,refund',
  `amount` decimal(10,2) NOT NULL COMMENT '金额',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `related_order_id` int(11) DEFAULT NULL COMMENT '关联订单ID',
  `related_order_type` varchar(20) DEFAULT NULL COMMENT '关联订单类型:purchase,customer',
  `operator_id` int(11) NOT NULL COMMENT '操作人ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_type` (`type`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_related_order` (`related_order_id`, `related_order_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI分拣系统财务记录表';

-- 10. 添加外键约束
ALTER TABLE `fs_ai_member_modules` ADD CONSTRAINT `fk_member_modules_member` FOREIGN KEY (`member_id`) REFERENCES `fs_ai_members` (`id`) ON DELETE CASCADE;
ALTER TABLE `fs_ai_purchase_orders` ADD CONSTRAINT `fk_purchase_orders_member` FOREIGN KEY (`member_id`) REFERENCES `fs_ai_members` (`id`) ON DELETE CASCADE;
ALTER TABLE `fs_ai_purchase_order_items` ADD CONSTRAINT `fk_purchase_items_order` FOREIGN KEY (`purchase_order_id`) REFERENCES `fs_ai_purchase_orders` (`id`) ON DELETE CASCADE;
ALTER TABLE `fs_ai_customer_orders` ADD CONSTRAINT `fk_customer_orders_member` FOREIGN KEY (`member_id`) REFERENCES `fs_ai_members` (`id`) ON DELETE CASCADE;
ALTER TABLE `fs_ai_customer_order_items` ADD CONSTRAINT `fk_customer_items_order` FOREIGN KEY (`customer_order_id`) REFERENCES `fs_ai_customer_orders` (`id`) ON DELETE CASCADE;
ALTER TABLE `fs_ai_inventory` ADD CONSTRAINT `fk_inventory_member` FOREIGN KEY (`member_id`) REFERENCES `fs_ai_members` (`id`) ON DELETE CASCADE;
ALTER TABLE `fs_ai_financial_records` ADD CONSTRAINT `fk_financial_member` FOREIGN KEY (`member_id`) REFERENCES `fs_ai_members` (`id`) ON DELETE CASCADE;

-- 11. 插入默认的AI分拣系统管理员 (基于现有的fs_admin表的管理员)
INSERT INTO `fs_ai_members` (`username`, `password`, `nickname`, `phone`, `email`, `parent_id`, `level`, `hierarchy_path`, `status`, `created_by`) VALUES
('ai_admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'AI分拣管理员', '13800138000', '<EMAIL>', 0, 1, '0', 1, 1);

-- 12. 为AI分拣管理员添加所有模块权限
INSERT INTO `fs_ai_member_modules` (`member_id`, `module_name`, `module_title`, `is_enabled`) VALUES
(1, 'storage', '入库管理', 1),
(1, 'sorting', '分拣管理', 1),
(1, 'order_entry', '录单管理', 1),
(1, 'finance', '财务管理', 1);
