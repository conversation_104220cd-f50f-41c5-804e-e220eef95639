<?php
// 更新管理员密码脚本
require_once 'vendor/autoload.php';

use think\facade\Db;

try {
    // 初始化ThinkPHP
    $app = new \think\App();
    $app->initialize();
    
    echo "正在更新管理员密码...\n";
    
    // 新密码
    $newPassword = '123456';
    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
    
    // 更新管理员密码
    $result = Db::table('fs_members')
        ->where('username', 'admin')
        ->update(['password' => $hashedPassword]);
    
    if ($result) {
        echo "✓ 管理员密码更新成功！\n";
        echo "新密码: {$newPassword}\n";
        
        // 验证更新
        $admin = Db::table('fs_members')->where('username', 'admin')->find();
        if ($admin && password_verify($newPassword, $admin['password'])) {
            echo "✓ 密码验证成功\n";
        } else {
            echo "✗ 密码验证失败\n";
        }
    } else {
        echo "✗ 密码更新失败\n";
    }
    
} catch (\Exception $e) {
    echo "❌ 更新失败: " . $e->getMessage() . "\n";
}
