<?php
declare(strict_types=1);

namespace app\common\model;

use think\Model;
use think\model\concern\SoftDelete;

/**
 * 会员模型
 */
class Member extends Model
{
    // 表名
    protected $table = 'fs_members';

    // 主键
    protected $pk = 'id';

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // 字段类型转换
    protected $type = [
        'parent_id' => 'integer',
        'level' => 'integer',
        'max_sub_accounts' => 'integer',
        'current_sub_accounts' => 'integer',
        'status' => 'integer',
        'expire_time' => 'datetime',
        'last_login_time' => 'datetime',
    ];

    // 隐藏字段
    protected $hidden = ['password'];

    // JSON字段
    protected $json = [];

    // 获取器 - 状态文本
    public function getStatusTextAttr($value, $data)
    {
        $status = [0 => '禁用', 1 => '启用'];
        return $status[$data['status']] ?? '未知';
    }

    // 获取器 - 等级文本
    public function getLevelTextAttr($value, $data)
    {
        $levels = [1 => '普通会员', 2 => '高级会员'];
        return $levels[$data['level']] ?? '未知';
    }

    // 获取器 - 到期状态
    public function getExpireStatusAttr($value, $data)
    {
        if (empty($data['expire_time'])) {
            return '永久';
        }
        
        $expireTime = strtotime($data['expire_time']);
        $now = time();
        
        if ($expireTime > $now) {
            $days = ceil(($expireTime - $now) / 86400);
            return $days > 0 ? "剩余{$days}天" : '今日到期';
        } else {
            return '已到期';
        }
    }

    // 获取器 - 是否到期
    public function getIsExpiredAttr($value, $data)
    {
        if (empty($data['expire_time'])) {
            return false;
        }
        
        return strtotime($data['expire_time']) <= time();
    }

    // 修改器 - 密码加密
    public function setPasswordAttr($value)
    {
        return password_hash($value, PASSWORD_DEFAULT);
    }

    // 关联上级会员
    public function parent()
    {
        return $this->belongsTo(Member::class, 'parent_id', 'id');
    }

    // 关联子会员
    public function children()
    {
        return $this->hasMany(Member::class, 'parent_id', 'id');
    }

    // 关联会员模块
    public function modules()
    {
        return $this->hasMany(MemberModule::class, 'member_id', 'id');
    }

    // 关联进货单
    public function purchaseOrders()
    {
        return $this->hasMany(PurchaseOrder::class, 'member_id', 'id');
    }

    // 关联客户订单
    public function customerOrders()
    {
        return $this->hasMany(CustomerOrder::class, 'member_id', 'id');
    }

    // 关联库存
    public function inventory()
    {
        return $this->hasMany(Inventory::class, 'member_id', 'id');
    }

    // 关联财务记录
    public function financialRecords()
    {
        return $this->hasMany(FinancialRecord::class, 'member_id', 'id');
    }

    /**
     * 验证密码
     */
    public function verifyPassword(string $password): bool
    {
        return password_verify($password, $this->getData('password'));
    }

    /**
     * 检查是否有权限访问模块
     */
    public function hasModulePermission(string $moduleName): bool
    {
        $module = $this->modules()
            ->where('module_name', $moduleName)
            ->where('is_enabled', 1)
            ->find();

        if (!$module) {
            return false;
        }

        // 检查模块是否到期
        if ($module->expire_time && strtotime($module->expire_time) <= time()) {
            return false;
        }

        return true;
    }

    /**
     * 获取可用的模块列表
     */
    public function getAvailableModules(): array
    {
        $modules = $this->modules()
            ->where('is_enabled', 1)
            ->select();

        $availableModules = [];
        foreach ($modules as $module) {
            // 检查模块是否到期
            if ($module->expire_time && strtotime($module->expire_time) <= time()) {
                continue;
            }
            $availableModules[] = $module->module_name;
        }

        return $availableModules;
    }

    /**
     * 检查是否可以创建子账号
     */
    public function canCreateSubAccount(): bool
    {
        return $this->current_sub_accounts < $this->max_sub_accounts;
    }

    /**
     * 增加子账号数量
     */
    public function incrementSubAccounts(): bool
    {
        if (!$this->canCreateSubAccount()) {
            return false;
        }

        $this->current_sub_accounts += 1;
        return $this->save();
    }

    /**
     * 减少子账号数量
     */
    public function decrementSubAccounts(): bool
    {
        if ($this->current_sub_accounts > 0) {
            $this->current_sub_accounts -= 1;
            return $this->save();
        }
        return true;
    }

    /**
     * 更新最后登录信息
     */
    public function updateLastLogin(string $ip = ''): bool
    {
        $this->last_login_time = date('Y-m-d H:i:s');
        $this->last_login_ip = $ip;
        return $this->save();
    }

    /**
     * 获取会员层级路径
     */
    public function getHierarchyPath(): array
    {
        $path = [$this->id];
        $current = $this;

        while ($current->parent_id > 0) {
            $current = $current->parent;
            if ($current) {
                array_unshift($path, $current->id);
            } else {
                break;
            }
        }

        return $path;
    }

    /**
     * 检查是否是指定会员的上级
     */
    public function isParentOf(int $memberId): bool
    {
        $member = self::find($memberId);
        if (!$member) {
            return false;
        }

        $path = $member->getHierarchyPath();
        return in_array($this->id, $path);
    }

    /**
     * 搜索器 - 用户名
     */
    public function searchUsernameAttr($query, $value)
    {
        $query->whereLike('username', '%' . $value . '%');
    }

    /**
     * 搜索器 - 昵称
     */
    public function searchNicknameAttr($query, $value)
    {
        $query->whereLike('nickname', '%' . $value . '%');
    }

    /**
     * 搜索器 - 状态
     */
    public function searchStatusAttr($query, $value)
    {
        $query->where('status', $value);
    }

    /**
     * 搜索器 - 等级
     */
    public function searchLevelAttr($query, $value)
    {
        $query->where('level', $value);
    }

    /**
     * 搜索器 - 上级会员
     */
    public function searchParentIdAttr($query, $value)
    {
        $query->where('parent_id', $value);
    }
}
