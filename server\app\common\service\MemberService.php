<?php
declare(strict_types=1);

namespace app\common\service;

use app\common\model\Member;
use app\common\model\MemberModule;

/**
 * 会员服务类
 */
class MemberService
{
    /**
     * 获取会员列表
     */
    public static function getList(array $params = []): array
    {
        $page = $params['current'] ?? 1;
        $limit = $params['size'] ?? 20;

        $query = Member::with(['parent', 'modules'])
            ->order('id desc');

        // 搜索条件
        if (!empty($params['username'])) {
            $query->whereLike('username', '%' . $params['username'] . '%');
        }

        if (!empty($params['nickname'])) {
            $query->whereLike('nickname', '%' . $params['nickname'] . '%');
        }

        if (isset($params['status']) && $params['status'] !== '') {
            $query->where('status', $params['status']);
        }

        if (isset($params['level']) && $params['level'] !== '') {
            $query->where('level', $params['level']);
        }

        if (isset($params['parent_id']) && $params['parent_id'] !== '') {
            $query->where('parent_id', $params['parent_id']);
        }

        $result = $query->paginate([
            'list_rows' => $limit,
            'page' => $page,
        ]);

        // 处理数据
        $list = [];
        foreach ($result->items() as $item) {
            $memberData = $item->toArray();
            
            // 添加模块信息
            $memberData['modules'] = MemberModule::getMemberModules($item->id);
            $memberData['available_modules'] = $item->getAvailableModules();
            
            $list[] = $memberData;
        }

        return [
            'list' => $list,
            'total' => $result->total(),
            'current' => $page,
            'size' => $limit,
        ];
    }

    /**
     * 创建会员
     */
    public static function create(array $data): Member
    {
        try {
            // 开启事务
            Member::startTrans();

            // 创建会员
            $member = Member::create([
                'parent_id' => $data['parent_id'] ?? 0,
                'username' => $data['username'],
                'password' => $data['password'],
                'nickname' => $data['nickname'] ?? '',
                'phone' => $data['phone'] ?? '',
                'email' => $data['email'] ?? '',
                'level' => $data['level'] ?? 1,
                'max_sub_accounts' => $data['max_sub_accounts'] ?? 0,
                'expire_time' => $data['expire_time'] ?? null,
                'status' => $data['status'] ?? 1,
            ]);

            // 如果有上级会员，增加其子账号数量
            if ($member->parent_id > 0) {
                $parent = Member::find($member->parent_id);
                if ($parent) {
                    $parent->incrementSubAccounts();
                }
            }

            // 设置模块权限
            if (!empty($data['modules'])) {
                MemberModule::setMemberModules($member->id, $data['modules']);
            }

            // 提交事务
            Member::commit();

            // 清除相关缓存
            CacheService::deleteMemberRelatedCache($member->id);

            return $member;
        } catch (\Exception $e) {
            // 回滚事务
            Member::rollback();
            throw $e;
        }
    }

    /**
     * 更新会员
     */
    public static function update(int $id, array $data): bool
    {
        try {
            // 开启事务
            Member::startTrans();

            $member = Member::find($id);
            if (!$member) {
                throw new \Exception('会员不存在');
            }

            // 更新基本信息
            $updateData = [];
            $allowFields = ['nickname', 'phone', 'email', 'level', 'max_sub_accounts', 'expire_time', 'status'];
            
            foreach ($allowFields as $field) {
                if (isset($data[$field])) {
                    $updateData[$field] = $data[$field];
                }
            }

            // 如果修改了密码
            if (!empty($data['password'])) {
                $updateData['password'] = $data['password'];
            }

            $member->save($updateData);

            // 更新模块权限
            if (isset($data['modules'])) {
                MemberModule::setMemberModules($member->id, $data['modules']);
            }

            // 提交事务
            Member::commit();

            // 清除相关缓存
            CacheService::deleteMemberRelatedCache($member->id);

            return true;
        } catch (\Exception $e) {
            // 回滚事务
            Member::rollback();
            throw $e;
        }
    }

    /**
     * 删除会员（级联删除）
     */
    public static function delete(int $id): bool
    {
        try {
            // 开启事务
            Member::startTrans();

            $member = Member::find($id);
            if (!$member) {
                throw new \Exception('会员不存在');
            }

            // 检查是否有子会员
            $childrenCount = Member::where('parent_id', $id)->count();
            if ($childrenCount > 0) {
                throw new \Exception('该会员下还有子账号，请先删除子账号');
            }

            // 如果有上级会员，减少其子账号数量
            if ($member->parent_id > 0) {
                $parent = Member::find($member->parent_id);
                if ($parent) {
                    $parent->decrementSubAccounts();
                }
            }

            // 删除会员（会自动级联删除相关数据）
            $member->delete();

            // 提交事务
            Member::commit();

            // 清除相关缓存
            CacheService::deleteMemberRelatedCache($id);

            return true;
        } catch (\Exception $e) {
            // 回滚事务
            Member::rollback();
            throw $e;
        }
    }

    /**
     * 获取会员详情
     */
    public static function getDetail(int $id): ?array
    {
        $member = Member::with(['parent', 'modules'])->find($id);
        if (!$member) {
            return null;
        }

        $memberData = $member->toArray();
        
        // 添加模块信息
        $memberData['modules'] = MemberModule::getMemberModules($member->id);
        $memberData['available_modules'] = $member->getAvailableModules();
        
        // 添加子账号信息
        $memberData['children'] = Member::where('parent_id', $id)
            ->field('id,username,nickname,status,created_at')
            ->select()
            ->toArray();

        return $memberData;
    }

    /**
     * 验证登录
     */
    public static function login(string $username, string $password): ?array
    {
        $member = Member::where('username', $username)
            ->where('status', 1)
            ->find();

        if (!$member || !$member->verifyPassword($password)) {
            return null;
        }

        // 检查会员是否到期
        if ($member->is_expired) {
            throw new \Exception('会员已到期，请联系管理员续费');
        }

        // 更新登录信息
        $member->updateLastLogin(request()->ip());

        // 获取会员信息
        $memberData = $member->toArray();
        $memberData['modules'] = MemberModule::getMemberModules($member->id);
        $memberData['available_modules'] = $member->getAvailableModules();

        // 缓存会员信息
        CacheService::setMemberInfo($member->id, $memberData);
        CacheService::setMemberModules($member->id, $memberData['modules']);

        return $memberData;
    }

    /**
     * 检查用户名是否存在
     */
    public static function checkUsername(string $username, int $excludeId = 0): bool
    {
        $query = Member::where('username', $username);
        
        if ($excludeId > 0) {
            $query->where('id', '<>', $excludeId);
        }

        return $query->count() > 0;
    }

    /**
     * 获取会员的层级结构
     */
    public static function getMemberHierarchy(int $memberId): array
    {
        $member = Member::find($memberId);
        if (!$member) {
            return [];
        }

        $hierarchy = [];
        $current = $member;

        // 向上查找
        while ($current) {
            array_unshift($hierarchy, [
                'id' => $current->id,
                'username' => $current->username,
                'nickname' => $current->nickname,
                'level' => $current->level,
            ]);

            if ($current->parent_id > 0) {
                $current = Member::find($current->parent_id);
            } else {
                break;
            }
        }

        return $hierarchy;
    }

    /**
     * 获取可选的上级会员列表
     */
    public static function getParentOptions(int $excludeId = 0): array
    {
        $query = Member::where('status', 1)
            ->where('max_sub_accounts', '>', 0)
            ->field('id,username,nickname,current_sub_accounts,max_sub_accounts');

        if ($excludeId > 0) {
            $query->where('id', '<>', $excludeId);
        }

        $members = $query->select();
        
        $options = [['value' => 0, 'label' => '无上级（管理员直接开通）']];
        
        foreach ($members as $member) {
            if ($member->canCreateSubAccount()) {
                $options[] = [
                    'value' => $member->id,
                    'label' => "{$member->username}({$member->nickname}) - {$member->current_sub_accounts}/{$member->max_sub_accounts}"
                ];
            }
        }

        return $options;
    }
}
