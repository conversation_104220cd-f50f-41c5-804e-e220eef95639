<?php
// 检查当前数据库状态脚本
require_once 'vendor/autoload.php';

use think\facade\Db;

try {
    // 初始化ThinkPHP
    $app = new \think\App();
    $app->initialize();
    
    echo "检查当前数据库状态...\n";
    echo "数据库名称: aifen\n\n";
    
    // 获取所有表
    $tables = Db::query("SHOW TABLES");
    
    echo "当前数据库中的表:\n";
    foreach ($tables as $table) {
        $tableName = array_values($table)[0];
        echo "- {$tableName}\n";
    }
    
    echo "\n检查管理员账号:\n";
    
    // 检查fs_admin表
    try {
        $admins = Db::table('fs_admin')->select();
        if (!empty($admins)) {
            echo "✓ fs_admin表存在，管理员账号:\n";
            foreach ($admins as $admin) {
                echo "  - ID: {$admin['id']}, 用户名: {$admin['username']}, 昵称: {$admin['nickname']}\n";
            }
        } else {
            echo "✓ fs_admin表存在但无数据\n";
        }
    } catch (\Exception $e) {
        echo "✗ fs_admin表不存在或查询失败\n";
    }
    
    // 检查fs_user表
    try {
        $userCount = Db::table('fs_user')->count();
        echo "✓ fs_user表存在，用户数量: {$userCount}\n";
    } catch (\Exception $e) {
        echo "✗ fs_user表不存在或查询失败\n";
    }
    
    // 检查角色权限系统
    try {
        $roleCount = Db::table('fs_role')->count();
        $menuCount = Db::table('fs_menu')->count();
        echo "✓ 角色权限系统存在 - 角色数: {$roleCount}, 菜单数: {$menuCount}\n";
    } catch (\Exception $e) {
        echo "✗ 角色权限系统表不存在\n";
    }
    
    echo "\n检查AI分拣系统相关表:\n";
    
    $aiTables = [
        'fs_members' => '会员表',
        'fs_member_modules' => '会员模块权限表',
        'fs_purchase_orders' => '进货单表',
        'fs_purchase_order_items' => '进货单商品明细表',
        'fs_customer_orders' => '客户订单表',
        'fs_customer_order_items' => '客户订单商品明细表',
        'fs_inventory' => '库存表',
        'fs_financial_records' => '财务记录表'
    ];
    
    $existingAiTables = [];
    foreach ($aiTables as $tableName => $description) {
        try {
            $exists = Db::query("SHOW TABLES LIKE '{$tableName}'");
            if (!empty($exists)) {
                echo "✓ {$tableName} ({$description}) - 存在\n";
                $existingAiTables[] = $tableName;
            } else {
                echo "✗ {$tableName} ({$description}) - 不存在\n";
            }
        } catch (\Exception $e) {
            echo "✗ {$tableName} ({$description}) - 检查失败\n";
        }
    }
    
    echo "\n建议:\n";
    if (count($existingAiTables) > 0) {
        echo "发现已存在的AI分拣系统表，建议:\n";
        echo "1. 保留现有的管理员系统 (fs_admin)\n";
        echo "2. 清理已创建的AI分拣表，重新设计\n";
        echo "3. 基于现有的RBAC系统扩展AI分拣功能\n";
    } else {
        echo "未发现AI分拣系统表，建议:\n";
        echo "1. 保留现有的完整电商系统\n";
        echo "2. 基于现有的fs_admin和权限系统扩展AI分拣功能\n";
        echo "3. 添加AI分拣相关的新表\n";
    }
    
} catch (\Exception $e) {
    echo "❌ 检查失败: " . $e->getMessage() . "\n";
}
