<?php
// 执行正确的AI分拣系统扩展脚本
require_once 'vendor/autoload.php';

use think\facade\Db;

try {
    // 初始化ThinkPHP
    $app = new \think\App();
    $app->initialize();
    
    echo "开始执行正确的AI分拣系统扩展...\n";
    echo "基于现有的fs_user表进行扩展，不重复创建用户系统\n";
    echo "目标数据库: aifen\n\n";
    
    // 读取SQL文件
    $sqlFile = '../ai_sorting_correct_extension.sql';
    if (!file_exists($sqlFile)) {
        echo "❌ SQL文件不存在: {$sqlFile}\n";
        exit(1);
    }

    $sql = file_get_contents($sqlFile);
    if (!$sql) {
        echo "❌ 无法读取SQL文件\n";
        exit(1);
    }

    echo "✓ 成功读取正确的扩展SQL文件\n";

    // 分割SQL语句
    $statements = splitSqlStatements($sql);
    echo "✓ 找到 " . count($statements) . " 条SQL语句\n\n";

    // 执行每条SQL语句
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $index => $statement) {
        $statement = trim($statement);
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }

        try {
            Db::execute($statement);
            $successCount++;
            
            // 显示执行的操作类型
            if (stripos($statement, 'DROP TABLE') !== false) {
                preg_match('/DROP TABLE.*?`([^`]+)`/i', $statement, $matches);
                $tableName = $matches[1] ?? 'unknown';
                echo "✓ 清理表: {$tableName}\n";
            } elseif (stripos($statement, 'CREATE TABLE') !== false) {
                preg_match('/CREATE TABLE.*?`([^`]+)`/i', $statement, $matches);
                $tableName = $matches[1] ?? 'unknown';
                echo "✓ 创建表: {$tableName}\n";
            } elseif (stripos($statement, 'ALTER TABLE') !== false && stripos($statement, 'ADD COLUMN') !== false) {
                preg_match('/ALTER TABLE.*?`([^`]+)`/i', $statement, $matches);
                $tableName = $matches[1] ?? 'unknown';
                echo "✓ 扩展表字段: {$tableName}\n";
            } elseif (stripos($statement, 'ALTER TABLE') !== false && stripos($statement, 'ADD CONSTRAINT') !== false) {
                preg_match('/ALTER TABLE.*?`([^`]+)`/i', $statement, $matches);
                $tableName = $matches[1] ?? 'unknown';
                echo "✓ 添加约束: {$tableName}\n";
            } elseif (stripos($statement, 'INSERT INTO') !== false) {
                preg_match('/INSERT INTO.*?`([^`]+)`/i', $statement, $matches);
                $tableName = $matches[1] ?? 'unknown';
                echo "✓ 插入数据: {$tableName}\n";
            } else {
                echo "✓ 执行成功: 语句 " . ($index + 1) . "\n";
            }
        } catch (\Exception $e) {
            $errorCount++;
            echo "✗ 执行失败: 语句 " . ($index + 1) . " - " . $e->getMessage() . "\n";
            // 继续执行其他语句
        }
    }

    echo "\n扩展完成！成功执行 {$successCount} 条语句";
    if ($errorCount > 0) {
        echo "，失败 {$errorCount} 条语句";
    }
    echo "\n\n";

    // 验证扩展结果
    verifyCorrectExtension();

} catch (\Exception $e) {
    echo "❌ 扩展失败: " . $e->getMessage() . "\n";
    exit(1);
}

/**
 * 分割SQL语句
 */
function splitSqlStatements(string $sql): array
{
    // 移除注释
    $sql = preg_replace('/--.*$/m', '', $sql);
    
    // 按分号分割，但要考虑字符串中的分号
    $statements = [];
    $current = '';
    $inString = false;
    $stringChar = '';
    
    for ($i = 0; $i < strlen($sql); $i++) {
        $char = $sql[$i];
        
        if (!$inString && ($char === '"' || $char === "'")) {
            $inString = true;
            $stringChar = $char;
        } elseif ($inString && $char === $stringChar) {
            $inString = false;
            $stringChar = '';
        } elseif (!$inString && $char === ';') {
            $statements[] = trim($current);
            $current = '';
            continue;
        }
        
        $current .= $char;
    }
    
    if (trim($current)) {
        $statements[] = trim($current);
    }
    
    return array_filter($statements);
}

/**
 * 验证正确的扩展结果
 */
function verifyCorrectExtension(): void
{
    echo "验证AI分拣系统正确扩展结果:\n";

    // 检查AI分拣系统表
    $aiTables = [
        'fs_user_modules' => '用户模块权限表',
        'fs_purchase_orders' => '进货单表',
        'fs_purchase_order_items' => '进货单商品明细表',
        'fs_customer_orders' => '客户订单表',
        'fs_customer_order_items' => '客户订单商品明细表',
        'fs_inventory' => '库存表',
        'fs_financial_records' => '财务记录表'
    ];

    $allTablesExist = true;
    foreach ($aiTables as $tableName => $description) {
        try {
            $exists = Db::query("SHOW TABLES LIKE '{$tableName}'");
            if (!empty($exists)) {
                echo "✓ {$tableName} ({$description})\n";
            } else {
                echo "✗ {$tableName} ({$description}) - 不存在\n";
                $allTablesExist = false;
            }
        } catch (\Exception $e) {
            echo "✗ {$tableName} ({$description}) - 检查失败\n";
            $allTablesExist = false;
        }
    }

    // 检查fs_user表是否已扩展
    echo "\n检查fs_user表扩展字段:\n";
    try {
        $columns = Db::query("SHOW COLUMNS FROM fs_user");
        $aiFields = ['parent_id', 'level', 'hierarchy_path', 'expire_time', 'is_ai_member'];
        
        foreach ($aiFields as $field) {
            $found = false;
            foreach ($columns as $column) {
                if ($column['Field'] === $field) {
                    $found = true;
                    break;
                }
            }
            if ($found) {
                echo "✓ fs_user.{$field} - 已添加\n";
            } else {
                echo "✗ fs_user.{$field} - 未添加\n";
                $allTablesExist = false;
            }
        }
    } catch (\Exception $e) {
        echo "✗ 检查fs_user表扩展字段失败\n";
        $allTablesExist = false;
    }

    if ($allTablesExist) {
        echo "\n检查AI分拣系统测试数据:\n";
        
        try {
            $testUser = Db::table('fs_user')->where('username', 'ai_test_user')->find();
            if ($testUser) {
                echo "✓ AI分拣测试用户存在 (ID: {$testUser['id']}, is_ai_member: {$testUser['is_ai_member']})\n";
                
                $modules = Db::table('fs_user_modules')->where('user_id', $testUser['id'])->select();
                echo "✓ 测试用户模块权限: " . count($modules) . " 个模块\n";
                foreach ($modules as $module) {
                    echo "  - {$module['module_name']}: {$module['module_title']}\n";
                }
            } else {
                echo "✗ AI分拣测试用户不存在\n";
            }
        } catch (\Exception $e) {
            echo "✗ 检查AI分拣系统测试数据失败: " . $e->getMessage() . "\n";
        }

        echo "\n验证原有系统完整性:\n";
        try {
            $adminCount = Db::table('fs_admin')->count();
            $userCount = Db::table('fs_user')->count();
            $roleCount = Db::table('fs_role')->count();
            echo "✓ 原有系统完整 - 管理员: {$adminCount}, 用户: {$userCount}, 角色: {$roleCount}\n";
        } catch (\Exception $e) {
            echo "✗ 原有系统检查失败\n";
        }

        echo "\n🎉 AI分拣系统正确扩展验证完成！\n";
        echo "架构说明:\n";
        echo "- 管理员系统: fs_admin (后台管理员开通会员)\n";
        echo "- 会员系统: fs_user (AI分拣系统会员，移动端可开分账号)\n";
        echo "- 测试账号: ai_test_user / password\n";
        echo "- 原有电商系统保持完整，可继续使用\n";
    } else {
        echo "\n❌ 部分表创建失败，请检查错误信息\n";
    }
}
