<?php
// 检查当前连接的数据库名称
require_once 'vendor/autoload.php';

use think\facade\Db;

try {
    // 初始化ThinkPHP
    $app = new \think\App();
    $app->initialize();
    
    // 检查当前连接的数据库
    $currentDb = Db::query("SELECT DATABASE() as db_name")[0]['db_name'];
    echo "当前连接的数据库: {$currentDb}\n";
    
    // 检查.env配置
    echo "\n.env文件配置:\n";
    echo "DATABASE=" . env('DATABASE') . "\n";
    
    // 检查aifen数据库是否存在
    $databases = Db::query("SHOW DATABASES");
    echo "\n可用的数据库:\n";
    foreach ($databases as $db) {
        $dbName = array_values($db)[0];
        echo "- {$dbName}\n";
        if ($dbName === 'aifen') {
            echo "  ✓ aifen数据库存在\n";
        }
    }
    
    // 如果当前不是aifen数据库，提示需要修正
    if ($currentDb !== 'aifen') {
        echo "\n❌ 错误：当前连接的是 {$currentDb} 数据库，应该连接 aifen 数据库！\n";
        echo "请检查.env文件中的DATABASE配置\n";
    } else {
        echo "\n✓ 正确：当前连接的是 aifen 数据库\n";
    }
    
} catch (\Exception $e) {
    echo "❌ 检查失败: " . $e->getMessage() . "\n";
}
