<?php
declare(strict_types=1);

namespace app\adminapi\controller;

use app\common\controller\BaseController;
use app\adminapi\validate\CustomerOrderValidate;
use app\common\service\OrderEntryService;
use app\common\model\CustomerOrder;
use think\response\Json;

/**
 * 录单管理控制器
 */
class OrderEntry extends BaseController
{
    /**
     * 客户订单列表
     */
    public function customerOrderList(): Json
    {
        $params = $this->request->param();
        $result = OrderEntryService::getCustomerOrderList($params);
        return $this->success($result);
    }

    /**
     * 创建客户订单
     */
    public function createCustomerOrder(): Json
    {
        $data = $this->request->param();

        try {
            validate(CustomerOrderValidate::class)->scene('create')->check($data);
            
            // 获取当前用户信息
            $userInfo = $this->getUserInfo();
            $data['member_id'] = $userInfo['id'];
            $data['created_by'] = $userInfo['id'];

            $result = OrderEntryService::createCustomerOrder($data);
            return $this->success($result, '创建成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 更新客户订单
     */
    public function updateCustomerOrder(): Json
    {
        $id = $this->request->param('id');
        $data = $this->request->param();

        try {
            validate(CustomerOrderValidate::class)->scene('update')->check($data);
            
            $result = OrderEntryService::updateCustomerOrder((int)$id, $data);
            return $this->success($result, '更新成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 删除客户订单
     */
    public function deleteCustomerOrder(): Json
    {
        $id = $this->request->param('id');

        try {
            $result = OrderEntryService::deleteCustomerOrder((int)$id);
            return $this->success($result, '删除成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取客户订单详情
     */
    public function customerOrderDetail(): Json
    {
        $id = $this->request->param('id');

        try {
            $result = OrderEntryService::getCustomerOrderDetail((int)$id);
            if (!$result) {
                return $this->error('订单不存在');
            }
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 管理员定价
     */
    public function setPricing(): Json
    {
        $data = $this->request->param();

        try {
            validate(CustomerOrderValidate::class)->scene('pricing')->check($data);
            
            // 获取当前用户信息
            $userInfo = $this->getUserInfo();
            $data['operator_id'] = $userInfo['id'];

            $result = OrderEntryService::setPricing($data);
            return $this->success($result, '定价成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 分配分拣员
     */
    public function assignSorter(): Json
    {
        $data = $this->request->param();

        try {
            // 获取当前用户信息
            $userInfo = $this->getUserInfo();
            $data['operator_id'] = $userInfo['id'];

            $result = OrderEntryService::assignSorter($data);
            return $this->success($result, '分配成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取可用分拣员列表
     */
    public function getSorterOptions(): Json
    {
        try {
            $result = OrderEntryService::getSorterOptions();
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 生成订单号
     */
    public function generateOrderNo(): Json
    {
        try {
            $orderNo = OrderEntryService::generateCustomerOrderNo();
            return $this->success(['order_no' => $orderNo]);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取待定价订单列表
     */
    public function pendingPricingOrders(): Json
    {
        try {
            // 获取当前用户信息
            $userInfo = $this->getUserInfo();

            $result = OrderEntryService::getPendingPricingOrders($userInfo['id']);
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取待分配订单列表
     */
    public function pendingAssignOrders(): Json
    {
        try {
            // 获取当前用户信息
            $userInfo = $this->getUserInfo();

            $result = OrderEntryService::getPendingAssignOrders($userInfo['id']);
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 批量定价
     */
    public function batchPricing(): Json
    {
        $data = $this->request->param();

        try {
            // 获取当前用户信息
            $userInfo = $this->getUserInfo();
            $data['operator_id'] = $userInfo['id'];

            $result = OrderEntryService::batchPricing($data);
            return $this->success($result, '批量定价成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 批量分配
     */
    public function batchAssign(): Json
    {
        $data = $this->request->param();

        try {
            // 获取当前用户信息
            $userInfo = $this->getUserInfo();
            $data['operator_id'] = $userInfo['id'];

            $result = OrderEntryService::batchAssign($data);
            return $this->success($result, '批量分配成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 取消订单
     */
    public function cancelOrder(): Json
    {
        $id = $this->request->param('id');
        $reason = $this->request->param('reason', '');

        try {
            $result = OrderEntryService::cancelOrder((int)$id, $reason);
            return $this->success($result, '订单已取消');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取订单统计
     */
    public function orderStats(): Json
    {
        try {
            // 获取当前用户信息
            $userInfo = $this->getUserInfo();

            $result = OrderEntryService::getOrderStats($userInfo['id']);
            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 获取当前用户信息
     */
    private function getUserInfo(): array
    {
        // 这里应该从JWT或session中获取用户信息
        // 暂时返回模拟数据，实际开发中需要实现
        return [
            'id' => 1,
            'username' => 'admin',
            'nickname' => '管理员'
        ];
    }
}
