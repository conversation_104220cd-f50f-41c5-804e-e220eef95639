<?php
declare(strict_types=1);

namespace app\adminapi\controller;

use app\common\controller\BaseController;
use app\common\service\CacheService;
use think\response\Json;

/**
 * 缓存管理控制器
 */
class Cache extends BaseController
{
    /**
     * 获取缓存统计信息
     */
    public function stats(): Json
    {
        try {
            $stats = CacheService::getCacheStats();
            $connectionStatus = CacheService::checkRedisConnection();
            
            $result = [
                'connection_status' => $connectionStatus,
                'cache_config' => $stats,
                'cache_keys_count' => count(CacheService::getCacheKeys()),
            ];
            
            return $this->success($result, '获取成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 检查Redis连接状态
     */
    public function checkConnection(): Json
    {
        try {
            $status = CacheService::checkRedisConnection();
            
            if ($status) {
                return $this->success(['status' => true], 'Redis连接正常');
            } else {
                return $this->error('Redis连接失败');
            }
        } catch (\Exception $e) {
            return $this->error('Redis连接异常：' . $e->getMessage());
        }
    }

    /**
     * 清除指定缓存
     */
    public function clear(): Json
    {
        try {
            $type = $this->request->param('type', 'all');
            
            switch ($type) {
                case 'member':
                    // 清除会员相关缓存
                    $memberId = $this->request->param('member_id');
                    if ($memberId) {
                        CacheService::deleteMemberRelatedCache((int)$memberId);
                        $message = '会员缓存清除成功';
                    } else {
                        return $this->error('请指定会员ID');
                    }
                    break;
                    
                case 'sorting':
                    // 清除分拣大厅缓存
                    CacheService::deleteSortingHallOrders();
                    $message = '分拣大厅缓存清除成功';
                    break;
                    
                case 'all':
                default:
                    // 清除所有缓存
                    CacheService::clearAll();
                    $message = '所有缓存清除成功';
                    break;
            }
            
            return $this->success([], $message);
        } catch (\Exception $e) {
            return $this->error('缓存清除失败：' . $e->getMessage());
        }
    }

    /**
     * 获取缓存键列表
     */
    public function keys(): Json
    {
        try {
            $pattern = $this->request->param('pattern', '*');
            $keys = CacheService::getCacheKeys($pattern);
            
            return $this->success([
                'keys' => $keys,
                'count' => count($keys)
            ], '获取成功');
        } catch (\Exception $e) {
            return $this->error($e->getMessage());
        }
    }

    /**
     * 测试缓存性能
     */
    public function performanceTest(): Json
    {
        try {
            $testData = [
                'test_key' => 'performance_test',
                'data' => str_repeat('test_data_', 1000),
                'timestamp' => time()
            ];
            
            // 写入测试
            $writeStart = microtime(true);
            CacheService::setMemberInfo(999999, $testData, 60);
            $writeTime = (microtime(true) - $writeStart) * 1000;
            
            // 读取测试
            $readStart = microtime(true);
            $result = CacheService::getMemberInfo(999999);
            $readTime = (microtime(true) - $readStart) * 1000;
            
            // 删除测试数据
            CacheService::deleteMemberInfo(999999);
            
            return $this->success([
                'write_time_ms' => round($writeTime, 2),
                'read_time_ms' => round($readTime, 2),
                'data_size_kb' => round(strlen(serialize($testData)) / 1024, 2),
                'test_success' => $result !== null
            ], '性能测试完成');
            
        } catch (\Exception $e) {
            return $this->error('性能测试失败：' . $e->getMessage());
        }
    }
}
