<?php
declare (strict_types = 1);

namespace {%namespace%};

use think\Request;

class {%className%}
{
    /**
     * 显示资源列表
     *
     * @return \think\Response
     */
    public function index{%actionSuffix%}()
    {
        //
    }

    /**
     * 显示创建资源表单页.
     *
     * @return \think\Response
     */
    public function create{%actionSuffix%}()
    {
        //
    }

    /**
     * 保存新建的资源
     *
     * @param  \think\Request  $request
     * @return \think\Response
     */
    public function save{%actionSuffix%}(Request $request)
    {
        //
    }

    /**
     * 显示指定的资源
     *
     * @param  int  $id
     * @return \think\Response
     */
    public function read{%actionSuffix%}($id)
    {
        //
    }

    /**
     * 显示编辑资源表单页.
     *
     * @param  int  $id
     * @return \think\Response
     */
    public function edit{%actionSuffix%}($id)
    {
        //
    }

    /**
     * 保存更新的资源
     *
     * @param  \think\Request  $request
     * @param  int  $id
     * @return \think\Response
     */
    public function update{%actionSuffix%}(Request $request, $id)
    {
        //
    }

    /**
     * 删除指定资源
     *
     * @param  int  $id
     * @return \think\Response
     */
    public function delete{%actionSuffix%}($id)
    {
        //
    }
}
