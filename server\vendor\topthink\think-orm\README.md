# ThinkORM4.0

基于PHP8.0+ 和PDO实现的轻量级ORM，完全重构的Model层，支持实体模型和分层（[新特性盘点](https://doc.thinkphp.cn/@think-orm/v4_0/new-features.html)），并基本兼容3.0（参考[升级指导](https://doc.thinkphp.cn/@think-orm/v4_0/upgrade.html)）。

## 特性

* 基于PDO和PHP强类型实现
* 原生查询支持
* 灵活的查询构造器和链式查询
* 自动参数绑定和预查询
* 聚合查询
* 强大的模型及关联定义
* 模型获取器和修改器
* 虚拟模型支持
* 实体模型和视图模型支持
* 支持ActiveRecord模式和仓储模式
* 模型事件和类型自动转换
* 数据自动写入和延迟写入
* 搜索器和查询范围
* 预载入关联查询和延迟关联查询
* 数据写入自动验证
* 多数据库和`MongoDb`支持
* 分布式及事务、断点重连
* `JSON`及枚举类支持
* `PSR-16`缓存及`PSR-3`日志规范

## 安装
~~~
composer require topthink/think-orm
~~~

## 文档

详细参考 [ThinkORM开发指南](https://doc.thinkphp.cn/@think-orm)

基于官方手册的数据训练和提供精准解答服务
[官方专家智能体](https://chat.topthink.com/chat/e7m6qe)

你可以使用官方提供的[ThinkChat](https://chat.topthink.com/)，让你在学习 ThinkPHP 的旅途中享受私人 AI 助理服务！

[![](https://www.topthink.com/uploads/assistant/20230630/4d1a3f0ad2958b49bb8189b7ef824cb0.png)](https://chat.topthink.com/)

ThinkPHP 生态服务由[顶想云](https://www.topthink.com)（TOPThink Cloud）提供，为生态提供专业的开发者服务和价值之选。