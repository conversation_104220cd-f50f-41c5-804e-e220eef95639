<?php
declare(strict_types=1);

namespace app\common\model;

use think\Model;

/**
 * 会员模块权限模型
 */
class MemberModule extends Model
{
    // 表名
    protected $table = 'fs_member_modules';

    // 主键
    protected $pk = 'id';

    // 自动时间戳
    protected $autoWriteTimestamp = true;

    // 字段类型转换
    protected $type = [
        'member_id' => 'integer',
        'is_enabled' => 'integer',
        'expire_time' => 'datetime',
    ];

    // 模块配置
    public static $modules = [
        'storage' => '入库管理',
        'sorting' => '分拣管理',
        'order_entry' => '录单管理',
        'finance' => '财务管理',
    ];

    // 获取器 - 启用状态文本
    public function getEnabledTextAttr($value, $data)
    {
        $status = [0 => '禁用', 1 => '启用'];
        return $status[$data['is_enabled']] ?? '未知';
    }

    // 获取器 - 到期状态
    public function getExpireStatusAttr($value, $data)
    {
        if (empty($data['expire_time'])) {
            return '永久';
        }
        
        $expireTime = strtotime($data['expire_time']);
        $now = time();
        
        if ($expireTime > $now) {
            $days = ceil(($expireTime - $now) / 86400);
            return $days > 0 ? "剩余{$days}天" : '今日到期';
        } else {
            return '已到期';
        }
    }

    // 获取器 - 是否到期
    public function getIsExpiredAttr($value, $data)
    {
        if (empty($data['expire_time'])) {
            return false;
        }
        
        return strtotime($data['expire_time']) <= time();
    }

    // 关联会员
    public function member()
    {
        return $this->belongsTo(Member::class, 'member_id', 'id');
    }

    /**
     * 检查模块是否可用
     */
    public function isAvailable(): bool
    {
        // 检查是否启用
        if (!$this->is_enabled) {
            return false;
        }

        // 检查是否到期
        if ($this->expire_time && strtotime($this->expire_time) <= time()) {
            return false;
        }

        return true;
    }

    /**
     * 获取所有可用模块
     */
    public static function getAvailableModules(): array
    {
        return self::$modules;
    }

    /**
     * 获取模块标题
     */
    public static function getModuleTitle(string $moduleName): string
    {
        return self::$modules[$moduleName] ?? $moduleName;
    }

    /**
     * 为会员开通模块
     */
    public static function enableModuleForMember(int $memberId, string $moduleName, ?string $expireTime = null): bool
    {
        if (!isset(self::$modules[$moduleName])) {
            return false;
        }

        $data = [
            'member_id' => $memberId,
            'module_name' => $moduleName,
            'module_title' => self::$modules[$moduleName],
            'is_enabled' => 1,
            'expire_time' => $expireTime,
        ];

        // 检查是否已存在
        $existing = self::where('member_id', $memberId)
            ->where('module_name', $moduleName)
            ->find();

        if ($existing) {
            // 更新现有记录
            return $existing->save($data);
        } else {
            // 创建新记录
            return self::create($data) !== false;
        }
    }

    /**
     * 为会员禁用模块
     */
    public static function disableModuleForMember(int $memberId, string $moduleName): bool
    {
        return self::where('member_id', $memberId)
            ->where('module_name', $moduleName)
            ->update(['is_enabled' => 0]) !== false;
    }

    /**
     * 获取会员的所有模块
     */
    public static function getMemberModules(int $memberId): array
    {
        $modules = self::where('member_id', $memberId)->select();
        
        $result = [];
        foreach ($modules as $module) {
            $result[$module->module_name] = [
                'id' => $module->id,
                'module_name' => $module->module_name,
                'module_title' => $module->module_title,
                'is_enabled' => $module->is_enabled,
                'enabled_text' => $module->enabled_text,
                'expire_time' => $module->expire_time,
                'expire_status' => $module->expire_status,
                'is_expired' => $module->is_expired,
                'is_available' => $module->isAvailable(),
            ];
        }

        return $result;
    }

    /**
     * 批量设置会员模块
     */
    public static function setMemberModules(int $memberId, array $modules): bool
    {
        try {
            // 开启事务
            self::startTrans();

            // 删除现有模块
            self::where('member_id', $memberId)->delete();

            // 添加新模块
            foreach ($modules as $moduleData) {
                if (!isset(self::$modules[$moduleData['module_name']])) {
                    continue;
                }

                $data = [
                    'member_id' => $memberId,
                    'module_name' => $moduleData['module_name'],
                    'module_title' => self::$modules[$moduleData['module_name']],
                    'is_enabled' => $moduleData['is_enabled'] ?? 1,
                    'expire_time' => $moduleData['expire_time'] ?? null,
                ];

                self::create($data);
            }

            // 提交事务
            self::commit();
            return true;
        } catch (\Exception $e) {
            // 回滚事务
            self::rollback();
            return false;
        }
    }

    /**
     * 搜索器 - 会员ID
     */
    public function searchMemberIdAttr($query, $value)
    {
        $query->where('member_id', $value);
    }

    /**
     * 搜索器 - 模块名称
     */
    public function searchModuleNameAttr($query, $value)
    {
        $query->where('module_name', $value);
    }

    /**
     * 搜索器 - 启用状态
     */
    public function searchIsEnabledAttr($query, $value)
    {
        $query->where('is_enabled', $value);
    }
}
