<?php
declare(strict_types=1);

namespace app\common\service;

use think\facade\Cache;

/**
 * 缓存服务类
 * 统一管理项目中的缓存操作
 */
class CacheService
{
    // 缓存键前缀常量
    const PREFIX_MEMBER = 'member:';
    const PREFIX_MEMBER_MODULES = 'member_modules:';
    const PREFIX_USER_SESSION = 'user_session:';
    const PREFIX_SORTING_HALL = 'sorting_hall:';
    const PREFIX_DAILY_STATS = 'daily_stats:';
    const PREFIX_ORDER_STATUS = 'order_status:';
    const PREFIX_PURCHASE_ORDER = 'purchase_order:';
    const PREFIX_FINANCIAL_STATS = 'financial_stats:';

    // 缓存时间常量（秒）
    const TTL_SHORT = 300;      // 5分钟
    const TTL_MEDIUM = 1800;    // 30分钟
    const TTL_LONG = 3600;      // 1小时
    const TTL_DAY = 86400;      // 24小时
    const TTL_WEEK = 604800;    // 7天

    /**
     * 获取会员信息缓存
     */
    public static function getMemberInfo(int $memberId): ?array
    {
        $key = self::PREFIX_MEMBER . $memberId;
        return Cache::get($key);
    }

    /**
     * 设置会员信息缓存
     */
    public static function setMemberInfo(int $memberId, array $memberInfo, int $ttl = self::TTL_LONG): bool
    {
        $key = self::PREFIX_MEMBER . $memberId;
        return Cache::set($key, $memberInfo, $ttl);
    }

    /**
     * 删除会员信息缓存
     */
    public static function deleteMemberInfo(int $memberId): bool
    {
        $key = self::PREFIX_MEMBER . $memberId;
        return Cache::delete($key);
    }

    /**
     * 获取会员模块权限缓存
     */
    public static function getMemberModules(int $memberId): ?array
    {
        $key = self::PREFIX_MEMBER_MODULES . $memberId;
        return Cache::get($key);
    }

    /**
     * 设置会员模块权限缓存
     */
    public static function setMemberModules(int $memberId, array $modules, int $ttl = self::TTL_LONG): bool
    {
        $key = self::PREFIX_MEMBER_MODULES . $memberId;
        return Cache::set($key, $modules, $ttl);
    }

    /**
     * 删除会员模块权限缓存
     */
    public static function deleteMemberModules(int $memberId): bool
    {
        $key = self::PREFIX_MEMBER_MODULES . $memberId;
        return Cache::delete($key);
    }

    /**
     * 获取用户会话缓存
     */
    public static function getUserSession(string $token): ?array
    {
        $key = self::PREFIX_USER_SESSION . $token;
        return Cache::get($key);
    }

    /**
     * 设置用户会话缓存
     */
    public static function setUserSession(string $token, array $userInfo, int $ttl = self::TTL_LONG): bool
    {
        $key = self::PREFIX_USER_SESSION . $token;
        return Cache::set($key, $userInfo, $ttl);
    }

    /**
     * 删除用户会话缓存
     */
    public static function deleteUserSession(string $token): bool
    {
        $key = self::PREFIX_USER_SESSION . $token;
        return Cache::delete($key);
    }

    /**
     * 获取分拣大厅订单缓存
     */
    public static function getSortingHallOrders(): ?array
    {
        $key = self::PREFIX_SORTING_HALL . 'orders';
        return Cache::get($key);
    }

    /**
     * 设置分拣大厅订单缓存
     */
    public static function setSortingHallOrders(array $orders, int $ttl = self::TTL_SHORT): bool
    {
        $key = self::PREFIX_SORTING_HALL . 'orders';
        return Cache::set($key, $orders, $ttl);
    }

    /**
     * 删除分拣大厅订单缓存
     */
    public static function deleteSortingHallOrders(): bool
    {
        $key = self::PREFIX_SORTING_HALL . 'orders';
        return Cache::delete($key);
    }

    /**
     * 获取每日统计数据缓存
     */
    public static function getDailyStats(string $date): ?array
    {
        $key = self::PREFIX_DAILY_STATS . $date;
        return Cache::get($key);
    }

    /**
     * 设置每日统计数据缓存
     */
    public static function setDailyStats(string $date, array $stats, int $ttl = self::TTL_DAY): bool
    {
        $key = self::PREFIX_DAILY_STATS . $date;
        return Cache::set($key, $stats, $ttl);
    }

    /**
     * 获取订单状态缓存
     */
    public static function getOrderStatus(int $orderId): ?array
    {
        $key = self::PREFIX_ORDER_STATUS . $orderId;
        return Cache::get($key);
    }

    /**
     * 设置订单状态缓存
     */
    public static function setOrderStatus(int $orderId, array $status, int $ttl = self::TTL_MEDIUM): bool
    {
        $key = self::PREFIX_ORDER_STATUS . $orderId;
        return Cache::set($key, $status, $ttl);
    }

    /**
     * 删除订单状态缓存
     */
    public static function deleteOrderStatus(int $orderId): bool
    {
        $key = self::PREFIX_ORDER_STATUS . $orderId;
        return Cache::delete($key);
    }

    /**
     * 获取财务统计缓存
     */
    public static function getFinancialStats(string $period): ?array
    {
        $key = self::PREFIX_FINANCIAL_STATS . $period;
        return Cache::get($key);
    }

    /**
     * 设置财务统计缓存
     */
    public static function setFinancialStats(string $period, array $stats, int $ttl = self::TTL_LONG): bool
    {
        $key = self::PREFIX_FINANCIAL_STATS . $period;
        return Cache::set($key, $stats, $ttl);
    }

    /**
     * 批量删除会员相关缓存（用于删除会员时）
     */
    public static function deleteMemberRelatedCache(int $memberId): bool
    {
        $keys = [
            self::PREFIX_MEMBER . $memberId,
            self::PREFIX_MEMBER_MODULES . $memberId,
        ];

        foreach ($keys as $key) {
            Cache::delete($key);
        }

        return true;
    }

    /**
     * 清除所有缓存
     */
    public static function clearAll(): bool
    {
        return Cache::clear();
    }

    /**
     * 获取缓存统计信息
     */
    public static function getCacheStats(): array
    {
        // 这里可以根据Redis驱动获取统计信息
        return [
            'driver' => 'redis',
            'prefix' => config('cache.stores.redis.prefix'),
            'host' => config('cache.stores.redis.host'),
            'port' => config('cache.stores.redis.port'),
        ];
    }

    /**
     * 检查Redis连接状态
     */
    public static function checkRedisConnection(): bool
    {
        try {
            Cache::set('connection_test', 'ok', 10);
            $result = Cache::get('connection_test');
            Cache::delete('connection_test');
            return $result === 'ok';
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 获取缓存键列表（调试用）
     */
    public static function getCacheKeys(string $pattern = '*'): array
    {
        try {
            // 获取Redis实例
            $redis = Cache::store('redis')->handler();
            $prefix = config('cache.stores.redis.prefix', '');
            return $redis->keys($prefix . $pattern);
        } catch (\Exception $e) {
            return [];
        }
    }
}
