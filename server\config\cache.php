<?php

// +----------------------------------------------------------------------
// | 缓存设置
// +----------------------------------------------------------------------

return [
    // 默认缓存驱动
    'default' => env('CACHE_DRIVER', 'redis'),

    // 缓存连接方式配置
    'stores'  => [
        'file' => [
            // 驱动方式
            'type'       => 'File',
            // 缓存保存目录
            'path'       => '',
            // 缓存前缀
            'prefix'     => '',
            // 缓存有效期 0表示永久缓存
            'expire'     => 0,
            // 缓存标签前缀
            'tag_prefix' => 'tag:',
            // 序列化机制 例如 ['serialize', 'unserialize']
            'serialize'  => [],
        ],
        'redis' => [
            // 驱动方式
            'type'        => 'Redis',
            // Redis服务器地址
            'host'        => env('REDIS_HOST', '127.0.0.1'),
            // Redis端口
            'port'        => env('REDIS_PORT', 6379),
            // Redis密码
            'password'    => env('REDIS_PASSWORD', ''),
            // Redis数据库索引
            'select'      => env('REDIS_SELECT', 0),
            // 连接超时时间
            'timeout'     => env('REDIS_TIMEOUT', 0),
            // 缓存有效期 0表示永久缓存
            'expire'      => env('REDIS_EXPIRE', 3600),
            // 是否持久连接
            'persistent'  => env('REDIS_PERSISTENT', false),
            // 缓存前缀
            'prefix'      => env('REDIS_PREFIX', 'fanshop:'),
            // 缓存标签前缀
            'tag_prefix'  => 'tag:',
            // 序列化机制
            'serialize'   => [],
        ],
        // 更多的缓存连接
    ],
];
