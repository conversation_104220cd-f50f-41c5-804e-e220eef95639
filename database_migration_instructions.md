# AI分拣系统数据库迁移说明

## 数据库配置
- 数据库名称：`aifen`
- 字符集：`utf8mb4`
- 排序规则：`utf8mb4_unicode_ci`

## 迁移步骤

### 1. 创建数据库（如果不存在）
```sql
CREATE DATABASE IF NOT EXISTS `aifen` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `aifen`;
```

### 2. 执行表结构创建
请在数据库管理工具中执行 `server/database/migrations/create_ai_sorting_tables.sql` 文件中的所有SQL语句。

### 3. 验证表创建
执行以下SQL验证表是否创建成功：
```sql
SHOW TABLES;
```

应该看到以下8个表：
- fs_members (会员表)
- fs_member_modules (会员模块权限表)
- fs_purchase_orders (进货单表)
- fs_purchase_order_items (进货单商品明细表)
- fs_customer_orders (客户订单表)
- fs_customer_order_items (客户订单商品明细表)
- fs_inventory (库存表)
- fs_financial_records (财务记录表)

### 4. 验证默认数据
执行以下SQL验证默认管理员账号是否创建：
```sql
SELECT * FROM fs_members WHERE username = 'admin';
SELECT * FROM fs_member_modules WHERE member_id = 1;
```

## 默认管理员账号
- 用户名：`admin`
- 密码：`password`（已加密存储）
- 拥有所有模块权限

## 注意事项
1. 确保数据库用户有足够的权限创建表和外键约束
2. 如果遇到外键约束错误，请确保先创建主表再创建从表
3. 所有表都使用级联删除，删除会员时会自动删除相关数据

## 下一步
数据库迁移完成后，可以：
1. 启动后端服务测试API接口
2. 使用默认管理员账号登录测试
3. 开始前端页面开发
