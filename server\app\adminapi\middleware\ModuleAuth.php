<?php
declare(strict_types=1);

namespace app\adminapi\middleware;

use app\common\service\CacheService;
use app\common\model\Member;
use Closure;
use think\Request;
use think\Response;

/**
 * 模块权限验证中间件
 */
class ModuleAuth
{
    // 模块路由映射
    protected $moduleRoutes = [
        'storage' => [
            'purchase-order',
            'storage-operation',
            'inventory',
        ],
        'sorting' => [
            'sorting',
            'sorting-task',
            'sorting-hall',
        ],
        'order_entry' => [
            'customer-order',
            'order-entry',
            'customer',
        ],
        'finance' => [
            'finance',
            'financial-record',
            'financial-stats',
        ],
    ];

    // 不需要模块权限验证的路由
    protected $excludeRoutes = [
        'member',
        'cache',
        'auth',
        'upload',
        'common',
    ];

    /**
     * 处理请求
     */
    public function handle(Request $request, Closure $next, string $module = '')
    {
        // 获取当前路由
        $route = $request->pathinfo();
        $routeParts = explode('/', trim($route, '/'));
        
        // 获取控制器名称
        $controller = $routeParts[1] ?? '';
        
        // 检查是否需要验证模块权限
        if ($this->shouldSkipAuth($controller)) {
            return $next($request);
        }

        // 获取当前用户信息
        $userInfo = $this->getCurrentUser($request);
        if (!$userInfo) {
            return $this->unauthorizedResponse('用户未登录');
        }

        // 确定需要验证的模块
        $requiredModule = $this->getRequiredModule($controller, $module);
        if (!$requiredModule) {
            return $next($request); // 不需要特定模块权限
        }

        // 验证模块权限
        if (!$this->hasModulePermission($userInfo['id'], $requiredModule)) {
            return $this->unauthorizedResponse("您没有访问【{$this->getModuleTitle($requiredModule)}】模块的权限");
        }

        return $next($request);
    }

    /**
     * 检查是否应该跳过权限验证
     */
    protected function shouldSkipAuth(string $controller): bool
    {
        return in_array($controller, $this->excludeRoutes);
    }

    /**
     * 获取当前用户信息
     */
    protected function getCurrentUser(Request $request): ?array
    {
        // 从请求头获取token
        $token = $request->header('Authorization', '');
        if (empty($token)) {
            return null;
        }

        // 移除Bearer前缀
        $token = str_replace('Bearer ', '', $token);

        // 从缓存获取用户信息
        $userInfo = CacheService::getUserSession($token);
        if ($userInfo) {
            return $userInfo;
        }

        // 如果缓存中没有，尝试从JWT解析
        try {
            $jwt = \Firebase\JWT\JWT::decode($token, new \Firebase\JWT\Key(config('jwt.key'), config('jwt.alg')));
            $payload = (array)$jwt;
            
            if (isset($payload['user_id'])) {
                $member = Member::find($payload['user_id']);
                if ($member && $member->status == 1 && !$member->is_expired) {
                    $userInfo = $member->toArray();
                    // 重新缓存用户信息
                    CacheService::setUserSession($token, $userInfo);
                    return $userInfo;
                }
            }
        } catch (\Exception $e) {
            // JWT解析失败
        }

        return null;
    }

    /**
     * 获取需要验证的模块
     */
    protected function getRequiredModule(string $controller, string $module = ''): ?string
    {
        // 如果明确指定了模块，直接返回
        if (!empty($module)) {
            return $module;
        }

        // 根据控制器名称自动判断模块
        foreach ($this->moduleRoutes as $moduleName => $routes) {
            if (in_array($controller, $routes)) {
                return $moduleName;
            }
        }

        return null;
    }

    /**
     * 检查用户是否有模块权限
     */
    protected function hasModulePermission(int $userId, string $module): bool
    {
        // 先从缓存获取
        $modules = CacheService::getMemberModules($userId);
        if ($modules !== null) {
            return isset($modules[$module]) && $modules[$module]['is_available'] ?? false;
        }

        // 缓存中没有，从数据库查询
        $member = Member::find($userId);
        if (!$member) {
            return false;
        }

        return $member->hasModulePermission($module);
    }

    /**
     * 获取模块标题
     */
    protected function getModuleTitle(string $module): string
    {
        $titles = [
            'storage' => '入库管理',
            'sorting' => '分拣管理',
            'order_entry' => '录单管理',
            'finance' => '财务管理',
        ];

        return $titles[$module] ?? $module;
    }

    /**
     * 返回未授权响应
     */
    protected function unauthorizedResponse(string $message): Response
    {
        return json([
            'code' => 403,
            'message' => $message,
            'data' => null
        ], 403);
    }
}
